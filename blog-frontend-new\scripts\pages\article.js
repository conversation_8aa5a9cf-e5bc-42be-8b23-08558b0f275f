import { api } from '../api.js';
import { appState } from '../state.js';
import { router } from '../router.js';
import { CONFIG } from '../config.js';
import { Utils } from '../utils.js';

// 文章详情页组件
export class ArticlePage {
    constructor() {
        this.article = null;
        this.comments = [];
        this.relatedArticles = [];
        this.currentPage = 1;
        this.totalPages = 1;
    }
    
    // 渲染文章详情页
    async render(params = {}, query = {}) {
        try {
            const articleId = params.id;
            if (!articleId) {
                throw new Error('文章ID不能为空');
            }
            
            // 显示加载状态
            appState.setLoading(true, '加载文章内容...');
            
            // 获取评论页码
            this.currentPage = parseInt(query.page) || 1;
            
            // 并行加载数据
            await Promise.all([
                this.loadArticle(articleId),
                this.loadComments(articleId, this.currentPage),
                this.loadRelatedArticles(articleId)
            ]);
            
            // 渲染页面内容
            this.renderContent();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 更新页面标题
            if (this.article) {
                document.title = `${this.article.title} - 现代博客系统`;
            }
            
        } catch (error) {
            console.error('Article page render error:', error);
            this.renderError(error);
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 加载文章详情
    async loadArticle(articleId) {
        try {
            this.article = await api.getArticle(articleId);
            
            // 更新应用状态
            appState.setCurrentArticle(this.article);
            
        } catch (error) {
            console.error('Failed to load article:', error);
            throw error;
        }
    }
    
    // 加载评论列表
    async loadComments(articleId, page = 1) {
        try {
            const response = await api.getComments(articleId, {
                page,
                limit: CONFIG.PAGINATION.COMMENTS_PAGE_SIZE || 10
            });
            
            this.comments = response.comments || response.data || [];
            this.currentPage = response.currentPage || page;
            this.totalPages = response.totalPages || Math.ceil((response.total || 0) / (CONFIG.PAGINATION.COMMENTS_PAGE_SIZE || 10));
            
        } catch (error) {
            console.error('Failed to load comments:', error);
            // 评论加载失败不影响文章显示
            this.comments = [];
        }
    }
    
    // 加载相关文章
    async loadRelatedArticles(articleId) {
        try {
            this.relatedArticles = await api.getRelatedArticles(articleId);
        } catch (error) {
            console.error('Failed to load related articles:', error);
            // 相关文章加载失败不影响主要内容
            this.relatedArticles = [];
        }
    }
    
    // 渲染页面内容
    renderContent() {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;
        
        mainContent.innerHTML = `
            <div class="article-page">
                <div class="container">
                    <div class="row">
                        <!-- 文章内容 -->
                        <div class="col-lg-8 col-md-12">
                            <div class="article-content">
                                ${this.renderArticleHeader()}
                                ${this.renderArticleBody()}
                                ${this.renderArticleFooter()}
                                ${this.renderCommentSection()}
                            </div>
                        </div>
                        
                        <!-- 侧边栏 -->
                        <div class="col-lg-4 col-md-12">
                            <div class="article-sidebar">
                                ${this.renderArticleMeta()}
                                ${this.renderRelatedArticles()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染文章头部
    renderArticleHeader() {
        if (!this.article) return '';
        
        const categoryName = this.article.category?.name || '未分类';
        const authorName = this.article.author?.name || '匿名';
        const imageUrl = this.article.featuredImage || '/images/default-article.jpg';
        
        return `
            <header class="article-header">
                <div class="article-category">
                    <span class="category-tag">${categoryName}</span>
                </div>
                
                <h1 class="article-title">${this.article.title}</h1>
                
                <div class="article-meta">
                    <div class="meta-item author">
                        <i class="fas fa-user"></i>
                        <span>${authorName}</span>
                    </div>
                    <div class="meta-item date">
                        <i class="fas fa-calendar"></i>
                        <span>${Utils.formatDate(this.article.createdAt, 'FULL')}</span>
                    </div>
                    <div class="meta-item views">
                        <i class="fas fa-eye"></i>
                        <span>${Utils.formatNumber(this.article.views || 0)} 次阅读</span>
                    </div>
                    <div class="meta-item comments">
                        <i class="fas fa-comments"></i>
                        <span>${Utils.formatNumber(this.article.commentCount || 0)} 条评论</span>
                    </div>
                </div>
                
                ${this.article.featuredImage ? `
                    <div class="article-image">
                        <img src="${imageUrl}" alt="${this.article.title}" loading="lazy">
                    </div>
                ` : ''}
            </header>
        `;
    }
    
    // 渲染文章正文
    renderArticleBody() {
        if (!this.article) return '';
        
        return `
            <div class="article-body">
                <div class="article-content-text">
                    ${this.formatArticleContent(this.article.content)}
                </div>
                
                ${this.article.tags && this.article.tags.length > 0 ? `
                    <div class="article-tags">
                        <h4>标签：</h4>
                        <div class="tag-list">
                            ${this.article.tags.map(tag => `
                                <a href="/?tag=${encodeURIComponent(tag)}" class="tag-item" data-route="/?tag=${encodeURIComponent(tag)}">
                                    #${tag}
                                </a>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    // 格式化文章内容
    formatArticleContent(content) {
        if (!content) return '';
        
        // 简单的Markdown渲染（可以后续集成专业的Markdown解析器）
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }
    
    // 渲染文章底部
    renderArticleFooter() {
        if (!this.article) return '';
        
        return `
            <footer class="article-footer">
                <div class="article-actions">
                    <button class="action-btn like-btn" data-action="like">
                        <i class="fas fa-heart"></i>
                        <span>点赞 (${this.article.likes || 0})</span>
                    </button>
                    <button class="action-btn share-btn" data-action="share">
                        <i class="fas fa-share"></i>
                        <span>分享</span>
                    </button>
                    <button class="action-btn bookmark-btn" data-action="bookmark">
                        <i class="fas fa-bookmark"></i>
                        <span>收藏</span>
                    </button>
                </div>
                
                <div class="article-navigation">
                    ${this.article.previousArticle ? `
                        <a href="/article/${this.article.previousArticle._id}" class="nav-link prev-article" data-route="/article/${this.article.previousArticle._id}">
                            <i class="fas fa-chevron-left"></i>
                            <div class="nav-content">
                                <span class="nav-label">上一篇</span>
                                <span class="nav-title">${this.article.previousArticle.title}</span>
                            </div>
                        </a>
                    ` : ''}
                    
                    ${this.article.nextArticle ? `
                        <a href="/article/${this.article.nextArticle._id}" class="nav-link next-article" data-route="/article/${this.article.nextArticle._id}">
                            <div class="nav-content">
                                <span class="nav-label">下一篇</span>
                                <span class="nav-title">${this.article.nextArticle.title}</span>
                            </div>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    ` : ''}
                </div>
            </footer>
        `;
    }
    
    // 渲染评论区
    renderCommentSection() {
        return `
            <section class="comment-section">
                <h3 class="section-title">
                    评论 (${this.article?.commentCount || 0})
                </h3>
                
                ${this.renderCommentForm()}
                ${this.renderCommentList()}
                ${this.renderCommentPagination()}
            </section>
        `;
    }
    
    // 渲染评论表单
    renderCommentForm() {
        const state = appState.getState();
        
        if (!state.isAuthenticated) {
            return `
                <div class="comment-form-placeholder">
                    <p>请 <button id="loginToComment" class="link-btn">登录</button> 后发表评论</p>
                </div>
            `;
        }
        
        return `
            <form class="comment-form" id="commentForm">
                <div class="form-group">
                    <textarea 
                        id="commentContent" 
                        name="content" 
                        placeholder="写下你的评论..." 
                        rows="4" 
                        required
                    ></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        发表评论
                    </button>
                </div>
            </form>
        `;
    }
    
    // 渲染评论列表
    renderCommentList() {
        if (!this.comments || this.comments.length === 0) {
            return `
                <div class="no-comments">
                    <i class="fas fa-comments"></i>
                    <p>暂无评论，快来发表第一条评论吧！</p>
                </div>
            `;
        }
        
        return `
            <div class="comment-list">
                ${this.comments.map(comment => this.renderComment(comment)).join('')}
            </div>
        `;
    }
    
    // 渲染单个评论
    renderComment(comment) {
        const authorName = comment.author?.name || '匿名用户';
        const avatar = comment.author?.avatar || '/images/default-avatar.png';
        
        return `
            <div class="comment-item" data-id="${comment._id}">
                <div class="comment-avatar">
                    <img src="${avatar}" alt="${authorName}">
                </div>
                <div class="comment-content">
                    <div class="comment-header">
                        <span class="comment-author">${authorName}</span>
                        <span class="comment-date">${Utils.formatDate(comment.createdAt, 'RELATIVE')}</span>
                    </div>
                    <div class="comment-text">
                        ${comment.content}
                    </div>
                    <div class="comment-actions">
                        <button class="action-btn reply-btn" data-action="reply" data-id="${comment._id}">
                            <i class="fas fa-reply"></i>
                            回复
                        </button>
                        <button class="action-btn like-btn" data-action="like" data-id="${comment._id}">
                            <i class="fas fa-thumbs-up"></i>
                            ${comment.likes || 0}
                        </button>
                    </div>
                    
                    ${comment.replies && comment.replies.length > 0 ? `
                        <div class="comment-replies">
                            ${comment.replies.map(reply => this.renderReply(reply)).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    // 渲染回复
    renderReply(reply) {
        const authorName = reply.author?.name || '匿名用户';
        const avatar = reply.author?.avatar || '/images/default-avatar.png';
        
        return `
            <div class="reply-item" data-id="${reply._id}">
                <div class="reply-avatar">
                    <img src="${avatar}" alt="${authorName}">
                </div>
                <div class="reply-content">
                    <div class="reply-header">
                        <span class="reply-author">${authorName}</span>
                        <span class="reply-date">${Utils.formatDate(reply.createdAt, 'RELATIVE')}</span>
                    </div>
                    <div class="reply-text">
                        ${reply.content}
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染评论分页
    renderCommentPagination() {
        if (this.totalPages <= 1) return '';
        
        const pagination = [];
        
        // 上一页
        if (this.currentPage > 1) {
            pagination.push(`
                <button class="page-btn prev-btn" data-page="${this.currentPage - 1}">
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>
            `);
        }
        
        // 页码
        for (let i = 1; i <= Math.min(this.totalPages, 5); i++) {
            const isActive = i === this.currentPage ? 'active' : '';
            pagination.push(`
                <button class="page-btn ${isActive}" data-page="${i}">
                    ${i}
                </button>
            `);
        }
        
        // 下一页
        if (this.currentPage < this.totalPages) {
            pagination.push(`
                <button class="page-btn next-btn" data-page="${this.currentPage + 1}">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>
            `);
        }
        
        return `
            <div class="comment-pagination">
                ${pagination.join('')}
            </div>
        `;
    }
    
    // 渲染文章元信息
    renderArticleMeta() {
        if (!this.article) return '';
        
        return `
            <div class="sidebar-widget article-meta-widget">
                <h3 class="widget-title">文章信息</h3>
                <div class="meta-list">
                    <div class="meta-item">
                        <span class="meta-label">发布时间：</span>
                        <span class="meta-value">${Utils.formatDate(this.article.createdAt, 'FULL')}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">最后更新：</span>
                        <span class="meta-value">${Utils.formatDate(this.article.updatedAt, 'FULL')}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">阅读量：</span>
                        <span class="meta-value">${Utils.formatNumber(this.article.views || 0)}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">字数：</span>
                        <span class="meta-value">${this.article.content?.length || 0}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染相关文章
    renderRelatedArticles() {
        if (!this.relatedArticles || this.relatedArticles.length === 0) return '';
        
        return `
            <div class="sidebar-widget related-articles-widget">
                <h3 class="widget-title">相关文章</h3>
                <ul class="related-list">
                    ${this.relatedArticles.slice(0, 5).map(article => `
                        <li class="related-item">
                            <a href="/article/${article._id}" class="related-link" data-route="/article/${article._id}">
                                <div class="related-title">${article.title}</div>
                                <div class="related-meta">
                                    <span class="related-date">${Utils.formatDate(article.createdAt, 'RELATIVE')}</span>
                                    <span class="related-views">${Utils.formatNumber(article.views || 0)} 阅读</span>
                                </div>
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 文章操作按钮
        document.addEventListener('click', this.handleArticleActions.bind(this));
        
        // 评论表单提交
        const commentForm = document.getElementById('commentForm');
        if (commentForm) {
            commentForm.addEventListener('submit', this.handleCommentSubmit.bind(this));
        }
        
        // 登录评论按钮
        const loginToComment = document.getElementById('loginToComment');
        if (loginToComment) {
            loginToComment.addEventListener('click', () => {
                // 触发登录模态框
                document.getElementById('loginBtn').click();
            });
        }
        
        // 评论分页
        document.addEventListener('click', this.handleCommentPagination.bind(this));
    }
    
    // 处理文章操作
    async handleArticleActions(event) {
        const actionBtn = event.target.closest('.action-btn');
        if (!actionBtn) return;
        
        const action = actionBtn.dataset.action;
        const commentId = actionBtn.dataset.id;
        
        try {
            switch (action) {
                case 'like':
                    await this.handleLike(commentId);
                    break;
                case 'share':
                    this.handleShare();
                    break;
                case 'bookmark':
                    await this.handleBookmark();
                    break;
                case 'reply':
                    this.handleReply(commentId);
                    break;
            }
        } catch (error) {
            console.error('Action error:', error);
            appState.addToast({
                type: 'error',
                message: error.message || '操作失败'
            });
        }
    }
    
    // 处理点赞
    async handleLike(commentId) {
        const state = appState.getState();
        if (!state.isAuthenticated) {
            appState.addToast({
                type: 'warning',
                message: '请先登录'
            });
            return;
        }
        
        if (commentId) {
            // 评论点赞
            await api.likeComment(commentId);
        } else {
            // 文章点赞
            await api.likeArticle(this.article._id);
        }
        
        appState.addToast({
            type: 'success',
            message: '点赞成功'
        });
    }
    
    // 处理分享
    handleShare() {
        if (navigator.share) {
            navigator.share({
                title: this.article.title,
                text: this.article.excerpt || this.article.title,
                url: window.location.href
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(window.location.href).then(() => {
                appState.addToast({
                    type: 'success',
                    message: '链接已复制到剪贴板'
                });
            });
        }
    }
    
    // 处理收藏
    async handleBookmark() {
        const state = appState.getState();
        if (!state.isAuthenticated) {
            appState.addToast({
                type: 'warning',
                message: '请先登录'
            });
            return;
        }
        
        // 这里需要实现收藏功能的API
        appState.addToast({
            type: 'success',
            message: '收藏成功'
        });
    }
    
    // 处理回复
    handleReply(commentId) {
        // 实现回复功能
        console.log('Reply to comment:', commentId);
    }
    
    // 处理评论提交
    async handleCommentSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const content = formData.get('content').trim();
        
        if (!content) {
            appState.addToast({
                type: 'warning',
                message: '请输入评论内容'
            });
            return;
        }
        
        try {
            appState.setLoading(true, '发表评论中...');
            
            await api.createComment(this.article._id, { content });
            
            appState.addToast({
                type: 'success',
                message: '评论发表成功'
            });
            
            // 重新加载评论
            await this.loadComments(this.article._id, 1);
            this.renderContent();
            this.setupEventListeners();
            
            // 清空表单
            event.target.reset();
            
        } catch (error) {
            console.error('Comment submit error:', error);
            appState.addToast({
                type: 'error',
                message: error.message || '评论发表失败'
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理评论分页
    handleCommentPagination(event) {
        const pageBtn = event.target.closest('.comment-pagination .page-btn');
        if (!pageBtn) return;
        
        const page = parseInt(pageBtn.dataset.page);
        if (page && page !== this.currentPage) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('page', page);
            router.navigate(currentUrl.pathname + currentUrl.search);
        }
    }
    
    // 渲染错误页面
    renderError(error) {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;
        
        mainContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>文章加载失败</h2>
                    <p>${error.message || '文章不存在或已被删除'}</p>
                    <div class="error-actions">
                        <a href="/" class="btn btn-primary" data-route="/">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                        <button onclick="location.reload()" class="btn btn-secondary">
                            <i class="fas fa-refresh"></i> 重新加载
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

// 创建文章详情页实例
const articlePage = new ArticlePage();

// 导出渲染函数
export const render = (params, query) => articlePage.render(params, query);

// 导出默认实例
export default articlePage;
