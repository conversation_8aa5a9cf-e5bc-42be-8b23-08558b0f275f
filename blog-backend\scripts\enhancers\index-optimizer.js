/**
 * 索引优化器
 * 
 * 自动创建和优化MongoDB索引以提高查询性能
 */

const mongoose = require('mongoose');
const colors = require('colors');

// 优化的索引配置
const OPTIMIZED_INDEXES = {
  // 文章集合索引
  articles: [
    // 基本索引
    { fields: { title: 'text', content: 'text', 'contentBlocks.content': 'text' }, options: { name: 'fulltext_search', weights: { title: 10, 'contentBlocks.content': 5, content: 1 } } },
    { fields: { createdAt: -1 }, options: { name: 'sort_by_date' } },
    { fields: { slug: 1 }, options: { unique: true, sparse: true } },
    { fields: { author: 1 }, options: {} },
    
    // 复合索引 - 高效过滤和排序
    { fields: { status: 1, createdAt: -1 }, options: { name: 'filter_by_status_and_date' } },
    { fields: { category: 1, status: 1, createdAt: -1 }, options: { name: 'filter_by_category_and_status' } },
    { fields: { tags: 1, status: 1 }, options: { name: 'filter_by_tags_and_status' } },
    { fields: { viewCount: -1, status: 1 }, options: { name: 'popular_articles' } },
    
    // 自定义元数据索引
    { fields: { 'metadata.featured': 1 }, options: { sparse: true, name: 'custom_featured' } },
    { fields: { 'metadata.position': 1 }, options: { sparse: true, name: 'custom_position' } },
    
    // 过期索引
    { fields: { scheduledPublishDate: 1 }, options: { sparse: true, name: 'scheduled_publishing' } }
  ],
  
  // 评论集合索引
  comments: [
    // 基本索引
    { fields: { article: 1, createdAt: -1 }, options: { name: 'comments_by_article' } },
    { fields: { author: 1, createdAt: -1 }, options: { name: 'comments_by_author' } },
    
    // 嵌套评论索引
    { fields: { parentComment: 1 }, options: { sparse: true, name: 'nested_comments' } },
    
    // 状态索引
    { fields: { status: 1, createdAt: -1 }, options: { name: 'filter_comments_by_status' } },
    
    // 点赞和互动索引
    { fields: { likes: -1 }, options: { name: 'popular_comments' } }
  ],
  
  // 用户集合索引
  users: [
    // 基本索引
    { fields: { username: 1 }, options: { unique: true } },
    { fields: { email: 1 }, options: { unique: true } },
    
    // 角色索引
    { fields: { role: 1 }, options: {} },
    
    // 最近活动
    { fields: { lastActive: -1 }, options: {} },
    
    // 全文搜索
    { fields: { username: 'text', name: 'text', bio: 'text' }, options: { name: 'user_search' } }
  ],
  
  // 类别集合索引
  categories: [
    // 基本索引
    { fields: { name: 1 }, options: { unique: true } },
    
    // 全文搜索
    { fields: { name: 'text', description: 'text' }, options: { name: 'category_search' } }
  ]
};

/**
 * 优化MongoDB索引
 * @param {Object} options - 优化选项
 * @param {boolean} dryRun - 仅显示不执行
 */
async function optimize(options, dryRun = false) {
  console.log(colors.cyan('\n=== 开始优化索引 ==='));
  
  try {
    // 显示配置
    console.log(colors.yellow('应用的索引优化:'));
    Object.entries(options).forEach(([key, value]) => {
      console.log(`  ${key}: ${value ? colors.green('启用') : colors.red('禁用')}`);
    });
    
    // 应用索引优化
    if (!dryRun) {
      // 获取所有集合
      const collections = Object.keys(OPTIMIZED_INDEXES);
      
      // 为每个集合创建优化索引
      for (const collection of collections) {
        await optimizeCollectionIndexes(collection, options);
      }
    } else {
      console.log(colors.yellow('\n[演示模式] 将优化以下集合的索引:'));
      Object.keys(OPTIMIZED_INDEXES).forEach(collection => {
        console.log(`  - ${collection}`);
      });
    }
    
    console.log(colors.green('\n✓ 索引优化完成!'));
  } catch (error) {
    console.error(colors.red('✗ 索引优化失败:'), error);
    throw error;
  }
}

/**
 * 优化特定集合的索引
 * @param {string} collectionName - 集合名称
 * @param {Object} options - 优化选项
 */
async function optimizeCollectionIndexes(collectionName, options) {
  console.log(colors.cyan(`\n正在优化 ${collectionName} 集合的索引...`));
  
  // 获取MongoDB集合
  const db = mongoose.connection.db;
  const collection = db.collection(collectionName);
  
  // 获取现有索引
  const existingIndexes = await collection.indexes();
  const existingIndexNames = existingIndexes.map(index => index.name);
  
  console.log(colors.yellow(`当前有 ${existingIndexes.length} 个索引`));
  
  // 处理优化索引
  const optimizedIndexes = OPTIMIZED_INDEXES[collectionName];
  if (!optimizedIndexes) {
    console.log(colors.yellow(`跳过 ${collectionName} - 无优化索引配置`));
    return;
  }
  
  // 遍历并创建优化索引
  for (const indexConfig of optimizedIndexes) {
    const indexName = indexConfig.options.name || generateIndexName(indexConfig.fields);
    
    // 检查是否已存在
    if (existingIndexNames.includes(indexName)) {
      console.log(colors.blue(`索引 ${indexName} 已存在`));
      continue;
    }
    
    // 应用特定优化选项
    if (options.enableTextSearch === false && Object.values(indexConfig.fields).includes('text')) {
      console.log(colors.yellow(`跳过文本索引 ${indexName} - 文本搜索已禁用`));
      continue;
    }
    
    // 创建索引
    try {
      await collection.createIndex(indexConfig.fields, {
        ...indexConfig.options,
        background: true // 后台创建索引
      });
      console.log(colors.green(`✓ 创建索引 ${indexName}`));
    } catch (error) {
      console.error(colors.red(`✗ 创建索引 ${indexName} 失败:`), error.message);
    }
  }
}

/**
 * 从字段生成索引名称
 * @param {Object} fields - 索引字段
 * @returns {string} - 索引名称
 */
function generateIndexName(fields) {
  return Object.entries(fields)
    .map(([field, value]) => `${field}_${value}`)
    .join('_');
}

module.exports = {
  optimize
}; 