const mongoose = require('mongoose');

const ArticleSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Please provide a title'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters']
    },
    content: {
      type: String,
      required: [true, 'Please provide content'],
      minlength: [10, 'Content should be at least 10 characters long']
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    tags: {
      type: [String],
      default: []
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: false
    },
    viewCount: {
      type: Number,
      default: 0
    },
    isPublished: {
      type: Boolean,
      default: false
    },
    publishDate: {
      type: Date,
      default: Date.now
    },
    updateDate: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for comments
ArticleSchema.virtual('comments', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'article',
  justOne: false
});

// Create indexes for faster queries and full-text search
// 复合文本索引 - 展示MongoDB强大的全文搜索能力
ArticleSchema.index({
  title: 'text',
  content: 'text',
  tags: 'text'
}, {
  weights: {
    title: 10,    // 标题权重最高
    tags: 5,      // 标签权重中等
    content: 1    // 内容权重最低
  },
  name: 'article_text_index'
});

// 其他性能优化索引
ArticleSchema.index({ tags: 1 });
ArticleSchema.index({ category: 1 });
ArticleSchema.index({ publishDate: -1 });
ArticleSchema.index({ isPublished: 1, publishDate: -1 });
ArticleSchema.index({ viewCount: -1 });
ArticleSchema.index({ author: 1, isPublished: 1 });

// Update the updateDate before saving if not new
ArticleSchema.pre('save', function(next) {
  if (!this.isNew) {
    this.updateDate = Date.now();
  }
  next();
});

module.exports = mongoose.model('Article', ArticleSchema); 