import { CONFIG } from './config.js';
import { Utils, eventBus } from './utils.js';

// 应用状态管理类
export class AppState {
    constructor() {
        this.state = {
            // 用户相关状态
            user: null,
            isAuthenticated: false,
            isAdmin: false,
            
            // 应用状态
            loading: false,
            error: null,
            theme: 'light',
            
            // 数据状态
            articles: [],
            currentArticle: null,
            categories: [],
            comments: [],
            searchResults: [],
            
            // 分页状态
            pagination: {
                currentPage: 1,
                totalPages: 1,
                totalItems: 0,
                pageSize: CONFIG.PAGINATION.DEFAULT_PAGE_SIZE
            },
            
            // 过滤和搜索状态
            filters: {
                category: null,
                tag: null,
                author: null,
                dateRange: null
            },
            searchQuery: '',
            
            // UI状态
            sidebarOpen: false,
            modalOpen: null,
            toasts: [],
            
            // 缓存状态
            cache: new Map()
        };
        
        this.listeners = new Set();
        this.middlewares = [];
        
        // 初始化状态
        this.init();
    }
    
    // 初始化状态
    init() {
        // 从本地存储恢复用户状态
        this.restoreUserState();
        
        // 从本地存储恢复主题
        this.restoreTheme();
        
        // 监听存储变化
        window.addEventListener('storage', this.handleStorageChange.bind(this));
    }
    
    // 恢复用户状态
    restoreUserState() {
        const token = Utils.storage.get(CONFIG.STORAGE_KEYS.TOKEN);
        const user = Utils.storage.get(CONFIG.STORAGE_KEYS.USER);
        
        if (token && user) {
            this.setState({
                user,
                isAuthenticated: true,
                isAdmin: user.role === 'admin'
            });
        }
    }
    
    // 恢复主题
    restoreTheme() {
        const theme = Utils.storage.get(CONFIG.STORAGE_KEYS.THEME, 'light');
        this.setState({ theme });
        this.applyTheme(theme);
    }
    
    // 应用主题
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
    }
    
    // 处理存储变化
    handleStorageChange(event) {
        if (event.key === CONFIG.STORAGE_KEYS.TOKEN) {
            if (!event.newValue) {
                // Token被删除，用户登出
                this.setState({
                    user: null,
                    isAuthenticated: false,
                    isAdmin: false
                });
            }
        }
    }
    
    // 获取状态
    getState() {
        return Utils.deepClone(this.state);
    }
    
    // 设置状态
    setState(newState) {
        const prevState = Utils.deepClone(this.state);
        
        // 应用中间件
        let processedState = newState;
        for (const middleware of this.middlewares) {
            processedState = middleware(processedState, prevState, this.state);
        }
        
        // 合并状态
        this.state = Utils.merge({}, this.state, processedState);
        
        // 通知监听器
        this.notifyListeners(this.state, prevState);
        
        // 发送全局事件
        eventBus.emit('stateChange', {
            current: this.state,
            previous: prevState,
            changes: processedState
        });
    }
    
    // 订阅状态变化
    subscribe(listener) {
        this.listeners.add(listener);
        
        // 返回取消订阅函数
        return () => {
            this.listeners.delete(listener);
        };
    }
    
    // 通知监听器
    notifyListeners(currentState, prevState) {
        this.listeners.forEach(listener => {
            try {
                listener(currentState, prevState);
            } catch (error) {
                console.error('State listener error:', error);
            }
        });
    }
    
    // 添加中间件
    use(middleware) {
        this.middlewares.push(middleware);
    }
    
    // ========== 用户相关操作 ==========
    
    // 设置用户
    setUser(user) {
        this.setState({
            user,
            isAuthenticated: !!user,
            isAdmin: user?.role === 'admin'
        });
        
        if (user) {
            Utils.storage.set(CONFIG.STORAGE_KEYS.USER, user);
        } else {
            Utils.storage.remove(CONFIG.STORAGE_KEYS.USER);
        }
    }
    
    // 清除用户
    clearUser() {
        this.setState({
            user: null,
            isAuthenticated: false,
            isAdmin: false
        });
        
        Utils.storage.remove(CONFIG.STORAGE_KEYS.TOKEN);
        Utils.storage.remove(CONFIG.STORAGE_KEYS.USER);
    }
    
    // ========== 加载状态操作 ==========
    
    // 设置加载状态
    setLoading(loading, message = '') {
        this.setState({ 
            loading: typeof loading === 'boolean' ? loading : true,
            loadingMessage: message
        });
    }
    
    // 设置错误状态
    setError(error) {
        this.setState({ 
            error: typeof error === 'string' ? error : error?.message || '未知错误',
            loading: false
        });
    }
    
    // 清除错误
    clearError() {
        this.setState({ error: null });
    }
    
    // ========== 数据操作 ==========
    
    // 设置文章列表
    setArticles(articles, pagination = null) {
        this.setState({ 
            articles,
            pagination: pagination ? { ...this.state.pagination, ...pagination } : this.state.pagination
        });
    }
    
    // 设置当前文章
    setCurrentArticle(article) {
        this.setState({ currentArticle: article });
    }
    
    // 设置分类列表
    setCategories(categories) {
        this.setState({ categories });
    }
    
    // 设置评论列表
    setComments(comments) {
        this.setState({ comments });
    }
    
    // 设置搜索结果
    setSearchResults(results, query = '') {
        this.setState({ 
            searchResults: results,
            searchQuery: query
        });
    }
    
    // ========== 过滤和搜索操作 ==========
    
    // 设置过滤器
    setFilter(key, value) {
        this.setState({
            filters: {
                ...this.state.filters,
                [key]: value
            }
        });
    }
    
    // 清除过滤器
    clearFilters() {
        this.setState({
            filters: {
                category: null,
                tag: null,
                author: null,
                dateRange: null
            }
        });
    }
    
    // ========== UI状态操作 ==========
    
    // 切换侧边栏
    toggleSidebar() {
        this.setState({ sidebarOpen: !this.state.sidebarOpen });
    }
    
    // 设置模态框
    setModal(modalName) {
        this.setState({ modalOpen: modalName });
    }
    
    // 关闭模态框
    closeModal() {
        this.setState({ modalOpen: null });
    }
    
    // 添加Toast通知
    addToast(toast) {
        const id = Utils.generateId('toast');
        const newToast = {
            id,
            type: 'info',
            duration: CONFIG.UI.TOAST_DURATION,
            ...toast
        };
        
        this.setState({
            toasts: [...this.state.toasts, newToast]
        });
        
        // 自动移除Toast
        if (newToast.duration > 0) {
            setTimeout(() => {
                this.removeToast(id);
            }, newToast.duration);
        }
        
        return id;
    }
    
    // 移除Toast通知
    removeToast(id) {
        this.setState({
            toasts: this.state.toasts.filter(toast => toast.id !== id)
        });
    }
    
    // 设置主题
    setTheme(theme) {
        this.setState({ theme });
        this.applyTheme(theme);
        Utils.storage.set(CONFIG.STORAGE_KEYS.THEME, theme);
    }
    
    // ========== 缓存操作 ==========
    
    // 设置缓存
    setCache(key, value, ttl = CONFIG.CACHE.TTL) {
        if (!CONFIG.CACHE.ENABLED) return;
        
        const cacheItem = {
            value,
            timestamp: Date.now(),
            ttl
        };
        
        this.state.cache.set(key, cacheItem);
        
        // 限制缓存大小
        if (this.state.cache.size > CONFIG.CACHE.MAX_SIZE) {
            const firstKey = this.state.cache.keys().next().value;
            this.state.cache.delete(firstKey);
        }
    }
    
    // 获取缓存
    getCache(key) {
        if (!CONFIG.CACHE.ENABLED) return null;
        
        const cacheItem = this.state.cache.get(key);
        if (!cacheItem) return null;
        
        // 检查是否过期
        if (Date.now() - cacheItem.timestamp > cacheItem.ttl) {
            this.state.cache.delete(key);
            return null;
        }
        
        return cacheItem.value;
    }
    
    // 清除缓存
    clearCache(key = null) {
        if (key) {
            this.state.cache.delete(key);
        } else {
            this.state.cache.clear();
        }
    }
}

// 创建全局状态实例
export const appState = new AppState();

// 导出默认状态实例
export default appState;
