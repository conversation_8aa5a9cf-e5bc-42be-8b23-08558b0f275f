import { api } from '../../api.js';
import { appState } from '../../state.js';
import { router } from '../../router.js';
import { CONFIG } from '../../config.js';
import { Utils } from '../../utils.js';

// 管理后台文章管理组件
export class AdminArticles {
    constructor() {
        this.articles = [];
        this.categories = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.totalArticles = 0;
        this.filters = {
            status: '',
            category: '',
            search: ''
        };
        this.selectedArticles = new Set();
    }
    
    // 渲染文章管理页面
    async render(params = {}, query = {}) {
        try {
            // 显示加载状态
            appState.setLoading(true, '加载文章列表...');
            
            // 解析查询参数
            this.parseQuery(query);
            
            // 并行加载数据
            await Promise.all([
                this.loadArticles(),
                this.loadCategories()
            ]);
            
            // 渲染页面内容
            this.renderContent();
            
            // 设置事件监听器
            this.setupEventListeners();
            
        } catch (error) {
            console.error('Articles page render error:', error);
            this.renderError(error);
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 解析查询参数
    parseQuery(query) {
        this.currentPage = parseInt(query.page) || 1;
        this.filters.status = query.status || '';
        this.filters.category = query.category || '';
        this.filters.search = query.search || '';
    }
    
    // 加载文章列表
    async loadArticles() {
        try {
            const params = {
                page: this.currentPage,
                limit: CONFIG.PAGINATION.ADMIN_PAGE_SIZE || 20,
                ...this.filters
            };
            
            // 移除空值
            Object.keys(params).forEach(key => {
                if (!params[key]) delete params[key];
            });
            
            const response = await api.getAllArticles(params);
            
            this.articles = response.articles || response.data || [];
            this.currentPage = response.currentPage || this.currentPage;
            this.totalPages = response.totalPages || Math.ceil((response.total || 0) / (CONFIG.PAGINATION.ADMIN_PAGE_SIZE || 20));
            this.totalArticles = response.total || 0;
            
        } catch (error) {
            console.error('Failed to load articles:', error);
            throw error;
        }
    }
    
    // 加载分类列表
    async loadCategories() {
        try {
            this.categories = await api.getCategories();
        } catch (error) {
            console.error('Failed to load categories:', error);
            this.categories = [];
        }
    }
    
    // 渲染页面内容
    renderContent() {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;
        
        adminContent.innerHTML = `
            <div class="articles-page">
                <div class="page-header">
                    <div class="page-title">
                        <h1>文章管理</h1>
                        <span class="page-subtitle">共 ${this.totalArticles} 篇文章</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="newArticleBtn">
                            <i class="fas fa-plus"></i>
                            新建文章
                        </button>
                    </div>
                </div>
                
                <!-- 筛选和搜索 -->
                ${this.renderFilters()}
                
                <!-- 批量操作 -->
                ${this.renderBatchActions()}
                
                <!-- 文章列表 -->
                ${this.renderArticlesList()}
                
                <!-- 分页 -->
                ${this.renderPagination()}
            </div>
        `;
    }
    
    // 渲染筛选器
    renderFilters() {
        return `
            <div class="filters-section">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>状态筛选：</label>
                        <select id="statusFilter" class="form-select">
                            <option value="">全部状态</option>
                            <option value="published" ${this.filters.status === 'published' ? 'selected' : ''}>已发布</option>
                            <option value="draft" ${this.filters.status === 'draft' ? 'selected' : ''}>草稿</option>
                            <option value="archived" ${this.filters.status === 'archived' ? 'selected' : ''}>已归档</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>分类筛选：</label>
                        <select id="categoryFilter" class="form-select">
                            <option value="">全部分类</option>
                            ${this.categories.map(category => `
                                <option value="${category._id}" ${this.filters.category === category._id ? 'selected' : ''}>
                                    ${category.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    
                    <div class="filter-group search-group">
                        <label>搜索文章：</label>
                        <div class="search-input-group">
                            <input type="text" id="searchInput" class="form-control" 
                                   placeholder="输入标题或内容关键词..." 
                                   value="${this.filters.search}">
                            <button class="btn btn-outline-secondary" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                            ${this.filters.search ? `
                                <button class="btn btn-outline-secondary" id="clearSearchBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染批量操作
    renderBatchActions() {
        const selectedCount = this.selectedArticles.size;
        
        return `
            <div class="batch-actions ${selectedCount > 0 ? 'active' : ''}">
                <div class="batch-info">
                    已选择 <span class="selected-count">${selectedCount}</span> 篇文章
                </div>
                <div class="batch-buttons">
                    <button class="btn btn-sm btn-success" id="batchPublishBtn" ${selectedCount === 0 ? 'disabled' : ''}>
                        <i class="fas fa-check"></i>
                        批量发布
                    </button>
                    <button class="btn btn-sm btn-warning" id="batchDraftBtn" ${selectedCount === 0 ? 'disabled' : ''}>
                        <i class="fas fa-edit"></i>
                        转为草稿
                    </button>
                    <button class="btn btn-sm btn-danger" id="batchDeleteBtn" ${selectedCount === 0 ? 'disabled' : ''}>
                        <i class="fas fa-trash"></i>
                        批量删除
                    </button>
                    <button class="btn btn-sm btn-secondary" id="clearSelectionBtn" ${selectedCount === 0 ? 'disabled' : ''}>
                        <i class="fas fa-times"></i>
                        取消选择
                    </button>
                </div>
            </div>
        `;
    }
    
    // 渲染文章列表
    renderArticlesList() {
        if (!this.articles || this.articles.length === 0) {
            return `
                <div class="no-articles">
                    <div class="no-content">
                        <i class="fas fa-file-alt"></i>
                        <h3>暂无文章</h3>
                        <p>还没有创建任何文章，点击上方按钮开始创建第一篇文章。</p>
                        <button class="btn btn-primary" onclick="document.getElementById('newArticleBtn').click()">
                            <i class="fas fa-plus"></i>
                            创建文章
                        </button>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="articles-table-container">
                <table class="articles-table">
                    <thead>
                        <tr>
                            <th class="checkbox-col">
                                <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                            </th>
                            <th class="title-col">标题</th>
                            <th class="category-col">分类</th>
                            <th class="status-col">状态</th>
                            <th class="stats-col">统计</th>
                            <th class="date-col">创建时间</th>
                            <th class="actions-col">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.articles.map(article => this.renderArticleRow(article)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // 渲染文章行
    renderArticleRow(article) {
        const isSelected = this.selectedArticles.has(article._id);
        const statusClass = this.getStatusClass(article.status);
        const statusText = this.getStatusText(article.status);
        const categoryName = article.category?.name || '未分类';
        
        return `
            <tr class="article-row ${isSelected ? 'selected' : ''}" data-id="${article._id}">
                <td class="checkbox-col">
                    <input type="checkbox" class="article-checkbox form-check-input" 
                           value="${article._id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td class="title-col">
                    <div class="article-title-cell">
                        <h4 class="article-title">
                            <a href="/admin/articles/edit/${article._id}" class="title-link" data-route="/admin/articles/edit/${article._id}">
                                ${article.title}
                            </a>
                        </h4>
                        <div class="article-excerpt">${article.excerpt || article.content?.substring(0, 100) + '...' || ''}</div>
                        <div class="article-meta">
                            作者：${article.author?.name || '未知'} | 
                            更新：${Utils.formatDate(article.updatedAt, 'RELATIVE')}
                        </div>
                    </div>
                </td>
                <td class="category-col">
                    <span class="category-badge">${categoryName}</span>
                </td>
                <td class="status-col">
                    <span class="status-badge status-${statusClass}">${statusText}</span>
                </td>
                <td class="stats-col">
                    <div class="article-stats">
                        <div class="stat-item">
                            <i class="fas fa-eye"></i>
                            ${Utils.formatNumber(article.views || 0)}
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-comments"></i>
                            ${Utils.formatNumber(article.commentCount || 0)}
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-heart"></i>
                            ${Utils.formatNumber(article.likes || 0)}
                        </div>
                    </div>
                </td>
                <td class="date-col">
                    <div class="date-info">
                        <div class="create-date">${Utils.formatDate(article.createdAt, 'SHORT')}</div>
                        <div class="update-date">更新：${Utils.formatDate(article.updatedAt, 'RELATIVE')}</div>
                    </div>
                </td>
                <td class="actions-col">
                    <div class="action-buttons">
                        <a href="/article/${article._id}" class="btn btn-sm btn-outline-info" 
                           target="_blank" title="预览">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="/admin/articles/edit/${article._id}" class="btn btn-sm btn-outline-primary" 
                           data-route="/admin/articles/edit/${article._id}" title="编辑">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-sm btn-outline-success toggle-status-btn" 
                                data-id="${article._id}" data-status="${article.status}" title="切换状态">
                            <i class="fas fa-${article.status === 'published' ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-btn" 
                                data-id="${article._id}" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
    
    // 获取状态样式类
    getStatusClass(status) {
        const statusMap = {
            'published': 'success',
            'draft': 'warning',
            'archived': 'secondary'
        };
        return statusMap[status] || 'secondary';
    }
    
    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'published': '已发布',
            'draft': '草稿',
            'archived': '已归档'
        };
        return statusMap[status] || '未知';
    }
    
    // 渲染分页
    renderPagination() {
        if (this.totalPages <= 1) return '';
        
        const pagination = [];
        const maxVisible = 5;
        const startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        const endPage = Math.min(this.totalPages, startPage + maxVisible - 1);
        
        // 上一页
        if (this.currentPage > 1) {
            pagination.push(`
                <button class="page-btn prev-btn" data-page="${this.currentPage - 1}">
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>
            `);
        }
        
        // 页码
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === this.currentPage ? 'active' : '';
            pagination.push(`
                <button class="page-btn ${isActive}" data-page="${i}">
                    ${i}
                </button>
            `);
        }
        
        // 下一页
        if (this.currentPage < this.totalPages) {
            pagination.push(`
                <button class="page-btn next-btn" data-page="${this.currentPage + 1}">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>
            `);
        }
        
        return `
            <div class="pagination-section">
                <div class="pagination-info">
                    显示第 ${(this.currentPage - 1) * (CONFIG.PAGINATION.ADMIN_PAGE_SIZE || 20) + 1} - 
                    ${Math.min(this.currentPage * (CONFIG.PAGINATION.ADMIN_PAGE_SIZE || 20), this.totalArticles)} 条，
                    共 ${this.totalArticles} 条记录
                </div>
                <div class="pagination-buttons">
                    ${pagination.join('')}
                </div>
            </div>
        `;
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 新建文章按钮
        const newArticleBtn = document.getElementById('newArticleBtn');
        if (newArticleBtn) {
            newArticleBtn.addEventListener('click', () => {
                router.navigate('/admin/articles/new');
            });
        }
        
        // 筛选器
        document.getElementById('statusFilter')?.addEventListener('change', this.handleFilterChange.bind(this));
        document.getElementById('categoryFilter')?.addEventListener('change', this.handleFilterChange.bind(this));
        
        // 搜索
        document.getElementById('searchBtn')?.addEventListener('click', this.handleSearch.bind(this));
        document.getElementById('searchInput')?.addEventListener('keypress', this.handleSearchKeypress.bind(this));
        document.getElementById('clearSearchBtn')?.addEventListener('click', this.handleClearSearch.bind(this));
        
        // 全选复选框
        document.getElementById('selectAllCheckbox')?.addEventListener('change', this.handleSelectAll.bind(this));
        
        // 文章复选框
        document.addEventListener('change', this.handleArticleSelect.bind(this));
        
        // 批量操作
        document.getElementById('batchPublishBtn')?.addEventListener('click', () => this.handleBatchAction('publish'));
        document.getElementById('batchDraftBtn')?.addEventListener('click', () => this.handleBatchAction('draft'));
        document.getElementById('batchDeleteBtn')?.addEventListener('click', () => this.handleBatchAction('delete'));
        document.getElementById('clearSelectionBtn')?.addEventListener('click', this.handleClearSelection.bind(this));
        
        // 单个操作
        document.addEventListener('click', this.handleSingleActions.bind(this));
        
        // 分页
        document.addEventListener('click', this.handlePagination.bind(this));
    }
    
    // 处理筛选器变化
    handleFilterChange() {
        this.filters.status = document.getElementById('statusFilter')?.value || '';
        this.filters.category = document.getElementById('categoryFilter')?.value || '';
        this.currentPage = 1;
        this.updateURL();
        this.loadAndRender();
    }
    
    // 处理搜索
    handleSearch() {
        const searchInput = document.getElementById('searchInput');
        this.filters.search = searchInput?.value.trim() || '';
        this.currentPage = 1;
        this.updateURL();
        this.loadAndRender();
    }
    
    // 处理搜索键盘事件
    handleSearchKeypress(event) {
        if (event.key === 'Enter') {
            this.handleSearch();
        }
    }
    
    // 处理清除搜索
    handleClearSearch() {
        this.filters.search = '';
        document.getElementById('searchInput').value = '';
        this.currentPage = 1;
        this.updateURL();
        this.loadAndRender();
    }
    
    // 处理全选
    handleSelectAll(event) {
        const isChecked = event.target.checked;
        const checkboxes = document.querySelectorAll('.article-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
            const articleId = checkbox.value;
            if (isChecked) {
                this.selectedArticles.add(articleId);
            } else {
                this.selectedArticles.delete(articleId);
            }
        });
        
        this.updateBatchActions();
    }
    
    // 处理文章选择
    handleArticleSelect(event) {
        if (!event.target.classList.contains('article-checkbox')) return;
        
        const articleId = event.target.value;
        const isChecked = event.target.checked;
        
        if (isChecked) {
            this.selectedArticles.add(articleId);
        } else {
            this.selectedArticles.delete(articleId);
        }
        
        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const allCheckboxes = document.querySelectorAll('.article-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.article-checkbox:checked');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCheckboxes.length === allCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
        }
        
        this.updateBatchActions();
    }
    
    // 更新批量操作按钮状态
    updateBatchActions() {
        const selectedCount = this.selectedArticles.size;
        const batchActions = document.querySelector('.batch-actions');
        const selectedCountSpan = document.querySelector('.selected-count');
        const batchButtons = document.querySelectorAll('.batch-actions button');
        
        if (batchActions) {
            batchActions.classList.toggle('active', selectedCount > 0);
        }
        
        if (selectedCountSpan) {
            selectedCountSpan.textContent = selectedCount;
        }
        
        batchButtons.forEach(button => {
            button.disabled = selectedCount === 0;
        });
    }
    
    // 处理批量操作
    async handleBatchAction(action) {
        const selectedIds = Array.from(this.selectedArticles);
        if (selectedIds.length === 0) return;
        
        const actionText = {
            'publish': '发布',
            'draft': '转为草稿',
            'delete': '删除'
        }[action];
        
        const confirmed = await this.showConfirm(
            `批量${actionText}`,
            `确定要${actionText} ${selectedIds.length} 篇文章吗？`,
            action === 'delete' ? 'danger' : 'primary'
        );
        
        if (!confirmed) return;
        
        try {
            appState.setLoading(true, `正在${actionText}文章...`);
            
            if (action === 'delete') {
                await api.deleteArticles(selectedIds);
            } else {
                await api.updateArticlesStatus(selectedIds, action === 'publish' ? 'published' : 'draft');
            }
            
            appState.addToast({
                type: 'success',
                message: `成功${actionText} ${selectedIds.length} 篇文章`
            });
            
            // 清除选择并重新加载
            this.selectedArticles.clear();
            await this.loadAndRender();
            
        } catch (error) {
            console.error('Batch action error:', error);
            appState.addToast({
                type: 'error',
                message: `批量${actionText}失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理清除选择
    handleClearSelection() {
        this.selectedArticles.clear();
        document.querySelectorAll('.article-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAllCheckbox').checked = false;
        this.updateBatchActions();
    }
    
    // 处理单个操作
    async handleSingleActions(event) {
        const target = event.target.closest('button');
        if (!target) return;
        
        const articleId = target.dataset.id;
        if (!articleId) return;
        
        if (target.classList.contains('toggle-status-btn')) {
            await this.handleToggleStatus(articleId, target.dataset.status);
        } else if (target.classList.contains('delete-btn')) {
            await this.handleDeleteArticle(articleId);
        }
    }
    
    // 处理切换状态
    async handleToggleStatus(articleId, currentStatus) {
        const newStatus = currentStatus === 'published' ? 'draft' : 'published';
        const actionText = newStatus === 'published' ? '发布' : '转为草稿';
        
        try {
            appState.setLoading(true, `正在${actionText}文章...`);
            
            await api.updateArticle(articleId, { status: newStatus });
            
            appState.addToast({
                type: 'success',
                message: `文章已${actionText}`
            });
            
            await this.loadAndRender();
            
        } catch (error) {
            console.error('Toggle status error:', error);
            appState.addToast({
                type: 'error',
                message: `${actionText}失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理删除文章
    async handleDeleteArticle(articleId) {
        const confirmed = await this.showConfirm(
            '删除文章',
            '确定要删除这篇文章吗？此操作不可撤销。',
            'danger'
        );
        
        if (!confirmed) return;
        
        try {
            appState.setLoading(true, '正在删除文章...');
            
            await api.deleteArticle(articleId);
            
            appState.addToast({
                type: 'success',
                message: '文章删除成功'
            });
            
            await this.loadAndRender();
            
        } catch (error) {
            console.error('Delete article error:', error);
            appState.addToast({
                type: 'error',
                message: `删除失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理分页
    handlePagination(event) {
        const pageBtn = event.target.closest('.page-btn');
        if (!pageBtn) return;
        
        const page = parseInt(pageBtn.dataset.page);
        if (page && page !== this.currentPage) {
            this.currentPage = page;
            this.updateURL();
            this.loadAndRender();
        }
    }
    
    // 更新URL
    updateURL() {
        const params = new URLSearchParams();
        
        if (this.currentPage > 1) params.set('page', this.currentPage);
        if (this.filters.status) params.set('status', this.filters.status);
        if (this.filters.category) params.set('category', this.filters.category);
        if (this.filters.search) params.set('search', this.filters.search);
        
        const queryString = params.toString();
        const newURL = '/admin/articles' + (queryString ? '?' + queryString : '');
        
        history.replaceState(null, null, newURL);
    }
    
    // 加载并重新渲染
    async loadAndRender() {
        try {
            await this.loadArticles();
            this.renderContent();
            this.setupEventListeners();
        } catch (error) {
            console.error('Load and render error:', error);
            this.renderError(error);
        }
    }
    
    // 显示确认对话框
    showConfirm(title, message, type = 'primary') {
        return new Promise((resolve) => {
            // 这里应该显示一个确认对话框
            // 暂时使用浏览器原生确认框
            const confirmed = confirm(`${title}\n\n${message}`);
            resolve(confirmed);
        });
    }
    
    // 渲染错误页面
    renderError(error) {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;
        
        adminContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>文章列表加载失败</h2>
                    <p>${error.message || '无法加载文章列表'}</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
            </div>
        `;
    }
}

// 创建文章管理实例
const adminArticles = new AdminArticles();

// 导出渲染函数
export const render = (params, query) => adminArticles.render(params, query);

// 导出默认实例
export default adminArticles;
