import { CONFIG } from './config.js';
import { appState } from './state.js';
import { eventBus } from './utils.js';

// 路由类
export class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.params = {};
        this.query = {};
        this.guards = [];
        this.middlewares = [];
        
        // 初始化路由
        this.init();
    }
    
    // 初始化路由
    init() {
        // 监听浏览器前进后退
        window.addEventListener('popstate', this.handlePopState.bind(this));
        
        // 监听链接点击
        document.addEventListener('click', this.handleLinkClick.bind(this));
        
        // 初始化路由表
        this.setupRoutes();
        
        // 处理初始路由
        this.handleRoute();
    }
    
    // 设置路由表
    setupRoutes() {
        // 公共路由
        this.addRoute('/', 'home');
        this.addRoute('/article/:id', 'article');
        this.addRoute('/category/:id', 'category');
        this.addRoute('/tag/:tag', 'tag');
        this.addRoute('/search', 'search');
        this.addRoute('/about', 'about');
        
        // 管理员路由
        this.addRoute('/admin', 'admin-dashboard', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/login', 'admin-login');
        this.addRoute('/admin/articles', 'admin-articles', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/articles/new', 'admin-article-editor', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/articles/edit/:id', 'admin-article-editor', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/categories', 'admin-categories', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/comments', 'admin-comments', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/users', 'admin-users', { requiresAuth: true, requiresAdmin: true });
        this.addRoute('/admin/settings', 'admin-settings', { requiresAuth: true, requiresAdmin: true });
    }
    
    // 添加路由
    addRoute(path, component, options = {}) {
        const route = {
            path,
            component,
            regex: this.pathToRegex(path),
            keys: this.extractKeys(path),
            ...options
        };
        
        this.routes.set(path, route);
    }
    
    // 路径转正则表达式
    pathToRegex(path) {
        const keys = [];
        const pattern = path
            .replace(/\//g, '\\/')
            .replace(/:([^\/]+)/g, (match, key) => {
                keys.push(key);
                return '([^/]+)';
            });
        
        return new RegExp(`^${pattern}$`);
    }
    
    // 提取路径参数键
    extractKeys(path) {
        const keys = [];
        const matches = path.match(/:([^\/]+)/g);
        if (matches) {
            matches.forEach(match => {
                keys.push(match.substring(1));
            });
        }
        return keys;
    }
    
    // 匹配路由
    matchRoute(pathname) {
        for (const [path, route] of this.routes) {
            const match = pathname.match(route.regex);
            if (match) {
                const params = {};
                route.keys.forEach((key, index) => {
                    params[key] = match[index + 1];
                });
                
                return {
                    route,
                    params
                };
            }
        }
        
        return null;
    }
    
    // 处理路由
    async handleRoute(pathname = window.location.pathname) {
        try {
            // 解析查询参数
            this.query = this.parseQuery(window.location.search);
            
            // 匹配路由
            const match = this.matchRoute(pathname);
            
            if (!match) {
                // 404处理
                this.handle404();
                return;
            }
            
            const { route, params } = match;
            this.params = params;
            
            // 执行路由守卫
            const canActivate = await this.executeGuards(route, params);
            if (!canActivate) {
                return;
            }
            
            // 执行中间件
            await this.executeMiddlewares(route, params);
            
            // 更新当前路由
            this.currentRoute = route;
            
            // 更新导航状态
            this.updateNavigation(pathname);
            
            // 加载组件
            await this.loadComponent(route.component, params, this.query);
            
            // 发送路由变化事件
            eventBus.emit('routeChange', {
                route,
                params,
                query: this.query,
                pathname
            });
            
        } catch (error) {
            console.error('Route handling error:', error);
            this.handleError(error);
        }
    }
    
    // 执行路由守卫
    async executeGuards(route, params) {
        // 检查认证要求
        if (route.requiresAuth && !appState.getState().isAuthenticated) {
            this.navigate('/admin/login');
            return false;
        }
        
        // 检查管理员权限
        if (route.requiresAdmin && !appState.getState().isAdmin) {
            appState.addToast({
                type: 'error',
                message: '权限不足，无法访问此页面'
            });
            this.navigate('/');
            return false;
        }
        
        // 执行自定义守卫
        for (const guard of this.guards) {
            const result = await guard(route, params);
            if (!result) {
                return false;
            }
        }
        
        return true;
    }
    
    // 执行中间件
    async executeMiddlewares(route, params) {
        for (const middleware of this.middlewares) {
            await middleware(route, params);
        }
    }
    
    // 加载组件
    async loadComponent(componentName, params, query) {
        try {
            // 显示加载状态
            appState.setLoading(true);
            
            // 动态导入组件
            let component;
            
            if (componentName.startsWith('admin-')) {
                // 管理后台组件
                const moduleName = componentName.replace('admin-', '');
                component = await import(`./pages/admin/${moduleName}.js`);
            } else {
                // 公共组件
                component = await import(`./pages/${componentName}.js`);
            }
            
            // 渲染组件
            if (component.default && typeof component.default.render === 'function') {
                await component.default.render(params, query);
            } else if (typeof component.render === 'function') {
                await component.render(params, query);
            }
            
        } catch (error) {
            console.error('Component loading error:', error);
            this.handleError(error);
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 更新导航状态
    updateNavigation(pathname) {
        // 更新导航链接状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href') || link.getAttribute('data-route');
            if (href === pathname || (href === '/' && pathname === '/')) {
                link.classList.add('active');
            }
        });
        
        // 更新页面标题
        this.updatePageTitle();
    }
    
    // 更新页面标题
    updatePageTitle() {
        const route = this.currentRoute;
        if (!route) return;
        
        let title = '现代博客系统';
        
        switch (route.component) {
            case 'home':
                title = '首页 - 现代博客系统';
                break;
            case 'article':
                title = '文章详情 - 现代博客系统';
                break;
            case 'admin-dashboard':
                title = '仪表板 - 管理后台';
                break;
            case 'admin-articles':
                title = '文章管理 - 管理后台';
                break;
            case 'admin-article-editor':
                title = '文章编辑 - 管理后台';
                break;
            default:
                title = '现代博客系统';
        }
        
        document.title = title;
    }
    
    // 解析查询参数
    parseQuery(search) {
        const query = {};
        if (search) {
            const params = new URLSearchParams(search);
            for (const [key, value] of params) {
                query[key] = value;
            }
        }
        return query;
    }
    
    // 处理链接点击
    handleLinkClick(event) {
        const link = event.target.closest('a[href], [data-route]');
        if (!link) return;
        
        const href = link.getAttribute('href') || link.getAttribute('data-route');
        if (!href || href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {
            return;
        }
        
        event.preventDefault();
        this.navigate(href);
    }
    
    // 处理浏览器前进后退
    handlePopState(event) {
        this.handleRoute();
    }
    
    // 导航到指定路径
    navigate(path, replace = false) {
        if (path === window.location.pathname) return;
        
        if (replace) {
            history.replaceState(null, null, path);
        } else {
            history.pushState(null, null, path);
        }
        
        this.handleRoute(path);
    }
    
    // 替换当前路径
    replace(path) {
        this.navigate(path, true);
    }
    
    // 返回上一页
    back() {
        history.back();
    }
    
    // 前进到下一页
    forward() {
        history.forward();
    }
    
    // 处理404
    handle404() {
        appState.setError('页面不存在');
        document.title = '页面不存在 - 现代博客系统';
        
        // 显示404页面
        const mainContent = document.getElementById('mainContent') || document.getElementById('adminContent');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="error-page">
                    <div class="error-content">
                        <h1>404</h1>
                        <h2>页面不存在</h2>
                        <p>抱歉，您访问的页面不存在。</p>
                        <a href="/" class="btn btn-primary">返回首页</a>
                    </div>
                </div>
            `;
        }
    }
    
    // 处理错误
    handleError(error) {
        appState.setError(error.message || '页面加载失败');
        
        const mainContent = document.getElementById('mainContent') || document.getElementById('adminContent');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="error-page">
                    <div class="error-content">
                        <h1>错误</h1>
                        <h2>页面加载失败</h2>
                        <p>${error.message || '未知错误'}</p>
                        <button onclick="location.reload()" class="btn btn-primary">重新加载</button>
                    </div>
                </div>
            `;
        }
    }
    
    // 添加路由守卫
    addGuard(guard) {
        this.guards.push(guard);
    }
    
    // 添加中间件
    addMiddleware(middleware) {
        this.middlewares.push(middleware);
    }
    
    // 获取当前路由信息
    getCurrentRoute() {
        return {
            route: this.currentRoute,
            params: this.params,
            query: this.query,
            pathname: window.location.pathname
        };
    }
}

// 创建全局路由实例
export const router = new Router();

// 导出默认路由实例
export default router;
