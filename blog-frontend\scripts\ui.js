// ========== UI管理器 ==========
class UIManager {
    constructor() {
        this.activeModals = new Set();
        this.toasts = [];
        this.init();
    }
    
    init() {
        this.initNavigation();
        this.initSearch();
        this.initModals();
        this.initScrollEffects();
        this.initResponsive();
    }
    
    // ========== 导航相关 ==========
    initNavigation() {
        const navToggle = document.getElementById('navToggle');
        const navMenu = document.getElementById('navMenu');
        
        // 移动端导航切换
        navToggle?.addEventListener('click', () => {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // 导航链接点击
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = link.getAttribute('href');
                
                if (target.startsWith('#')) {
                    this.scrollToSection(target.substring(1));
                    
                    // 移动端关闭菜单
                    if (Utils.isMobile()) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                    
                    // 更新活动状态
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                }
            });
        });
        
        // 滚动时更新导航状态
        this.updateNavOnScroll();
    }
    
    // 滚动时更新导航
    updateNavOnScroll() {
        const navbar = document.querySelector('.navbar');
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');
        
        window.addEventListener('scroll', Utils.throttle(() => {
            // 导航栏背景透明度
            const scrollY = window.scrollY;
            if (scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
            
            // 更新活动导航链接
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                if (scrollY >= sectionTop && scrollY < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        }, 100));
    }
    
    // 滚动到指定区域
    scrollToSection(sectionId) {
        const element = document.getElementById(sectionId);
        if (element) {
            Utils.scrollToElement(element);
        }
    }
    
    // ========== 搜索相关 ==========
    initSearch() {
        const searchToggle = document.getElementById('searchToggle');
        const searchOverlay = document.getElementById('searchOverlay');
        const searchClose = document.getElementById('searchClose');
        const searchInput = document.getElementById('searchInput');
        const searchSuggestions = document.getElementById('searchSuggestions');
        
        // 打开搜索
        searchToggle?.addEventListener('click', () => {
            this.openSearch();
        });
        
        // 关闭搜索
        searchClose?.addEventListener('click', () => {
            this.closeSearch();
        });
        
        // 点击覆盖层关闭
        searchOverlay?.addEventListener('click', (e) => {
            if (e.target === searchOverlay) {
                this.closeSearch();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && searchOverlay.classList.contains('active')) {
                this.closeSearch();
            }
        });
        
        // 搜索输入处理
        if (searchInput) {
            const debouncedSearch = Utils.debounce(async (query) => {
                if (query.length >= CONFIG.SEARCH.MIN_QUERY_LENGTH) {
                    await this.performSearch(query);
                } else {
                    this.clearSearchSuggestions();
                }
            }, CONFIG.SEARCH.DEBOUNCE_DELAY);
            
            searchInput.addEventListener('input', (e) => {
                debouncedSearch(e.target.value.trim());
            });
            
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearchSubmit(searchInput.value.trim());
                }
            });
        }
    }
    
    // 打开搜索
    openSearch() {
        const searchOverlay = document.getElementById('searchOverlay');
        const searchInput = document.getElementById('searchInput');
        
        searchOverlay.classList.add('active');
        setTimeout(() => {
            searchInput?.focus();
        }, 300);
    }
    
    // 关闭搜索
    closeSearch() {
        const searchOverlay = document.getElementById('searchOverlay');
        const searchInput = document.getElementById('searchInput');
        
        searchOverlay.classList.remove('active');
        searchInput.value = '';
        this.clearSearchSuggestions();
    }
    
    // 执行搜索
    async performSearch(query) {
        try {
            const response = await API.searchArticles({ q: query, limit: CONFIG.SEARCH.MAX_SUGGESTIONS });
            this.displaySearchSuggestions(response.articles || []);
        } catch (error) {
            console.error('Search error:', error);
            this.clearSearchSuggestions();
        }
    }
    
    // 显示搜索建议
    displaySearchSuggestions(articles) {
        const searchSuggestions = document.getElementById('searchSuggestions');
        
        if (articles.length === 0) {
            searchSuggestions.innerHTML = '<div class="search-no-results">未找到相关文章</div>';
            return;
        }
        
        const suggestionsHTML = articles.map(article => `
            <div class="search-suggestion" onclick="UI.openArticle('${article._id}')">
                <div class="suggestion-title">${article.title}</div>
                <div class="suggestion-excerpt">${Utils.truncateText(article.content, 100)}</div>
                <div class="suggestion-meta">
                    <span class="suggestion-category">${article.category}</span>
                    <span class="suggestion-date">${Utils.formatDate(article.createdAt, 'SHORT')}</span>
                </div>
            </div>
        `).join('');
        
        searchSuggestions.innerHTML = suggestionsHTML;
    }
    
    // 清除搜索建议
    clearSearchSuggestions() {
        const searchSuggestions = document.getElementById('searchSuggestions');
        searchSuggestions.innerHTML = '';
    }
    
    // 处理搜索提交
    handleSearchSubmit(query) {
        if (query) {
            this.closeSearch();
            // 跳转到搜索结果页面或显示搜索结果
            this.showSearchResults(query);
        }
    }
    
    // 显示搜索结果
    async showSearchResults(query) {
        try {
            this.showLoading();
            const response = await API.searchArticles({ q: query });
            this.hideLoading();
            
            // 更新文章列表显示搜索结果
            this.displayArticles(response.articles || [], `搜索结果: "${query}"`);
            this.scrollToSection('articles');
        } catch (error) {
            this.hideLoading();
            this.showToast(handleApiError(error), 'error');
        }
    }
    
    // ========== 模态框相关 ==========
    initModals() {
        // 点击模态框外部关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModals.size > 0) {
                const lastModal = Array.from(this.activeModals).pop();
                this.closeModal(lastModal);
            }
        });
    }
    
    // 打开模态框
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            this.activeModals.add(modalId);
            document.body.style.overflow = 'hidden';
        }
    }
    
    // 关闭模态框
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            this.activeModals.delete(modalId);
            
            if (this.activeModals.size === 0) {
                document.body.style.overflow = '';
            }
        }
    }
    
    // ========== 加载状态 ==========
    showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.add('active');
    }
    
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.remove('active');
    }
    
    // ========== 消息提示 ==========
    showToast(message, type = 'info', duration = CONFIG.UI.TOAST_DURATION) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="toast-icon ${this.getToastIcon(type)}"></i>
                <span class="toast-message">${message}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: '5000',
            minWidth: '300px',
            maxWidth: '500px',
            padding: '16px',
            backgroundColor: this.getToastColor(type),
            color: 'white',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
            backdropFilter: 'blur(20px)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease-out',
            fontSize: '14px'
        });
        
        document.body.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
        
        this.toasts.push(toast);
    }
    
    // 获取消息图标
    getToastIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    // 获取消息颜色
    getToastColor(type) {
        const colors = {
            success: '#34C759',
            error: '#FF3B30',
            warning: '#FF9500',
            info: '#007AFF'
        };
        return colors[type] || colors.info;
    }
    
    // ========== 滚动效果 ==========
    initScrollEffects() {
        // 滚动指示器点击
        document.querySelector('.hero-scroll-indicator')?.addEventListener('click', () => {
            this.scrollToSection('articles');
        });
        
        // 平滑滚动到顶部
        this.addBackToTopButton();
    }
    
    // 添加返回顶部按钮
    addBackToTopButton() {
        const backToTop = document.createElement('button');
        backToTop.className = 'back-to-top';
        backToTop.innerHTML = '<i class="fas fa-chevron-up"></i>';
        backToTop.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
        `;
        
        backToTop.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        document.body.appendChild(backToTop);
        
        // 滚动时显示/隐藏按钮
        window.addEventListener('scroll', Utils.throttle(() => {
            if (window.scrollY > 500) {
                backToTop.style.opacity = '1';
                backToTop.style.visibility = 'visible';
            } else {
                backToTop.style.opacity = '0';
                backToTop.style.visibility = 'hidden';
            }
        }, 100));
    }
    
    // ========== 响应式处理 ==========
    initResponsive() {
        window.addEventListener('resize', Utils.throttle(() => {
            this.handleResize();
        }, 250));
    }
    
    handleResize() {
        // 移动端菜单处理
        if (!Utils.isMobile()) {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');
            
            navToggle?.classList.remove('active');
            navMenu?.classList.remove('active');
        }
    }
}

// ========== 全局UI函数 ==========
window.scrollToSection = function(sectionId) {
    UI.scrollToSection(sectionId);
};

// ========== 初始化UI管理器 ==========
window.UI = new UIManager();
