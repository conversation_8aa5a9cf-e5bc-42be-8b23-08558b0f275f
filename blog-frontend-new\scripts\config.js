// 应用配置
export const CONFIG = {
    // API配置
    API: {
        BASE_URL: 'http://localhost:5000/api',
        TIMEOUT: 10000,
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000
    },
    
    // 分页配置
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 10,
        MAX_PAGE_SIZE: 50,
        SHOW_PAGE_NUMBERS: 5
    },
    
    // UI配置
    UI: {
        TOAST_DURATION: 3000,
        LOADING_DELAY: 300,
        ANIMATION_DURATION: 300,
        DEBOUNCE_DELAY: 500
    },
    
    // 缓存配置
    CACHE: {
        ENABLED: true,
        TTL: 5 * 60 * 1000, // 5分钟
        MAX_SIZE: 100
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        TOKEN: 'blog_token',
        USER: 'blog_user',
        THEME: 'blog_theme',
        CACHE: 'blog_cache'
    },
    
    // 路由配置
    ROUTES: {
        PUBLIC: [
            '/',
            '/article/:id',
            '/category/:id',
            '/tag/:tag',
            '/search',
            '/about'
        ],
        ADMIN: [
            '/admin',
            '/admin/articles',
            '/admin/articles/new',
            '/admin/articles/edit/:id',
            '/admin/categories',
            '/admin/comments',
            '/admin/users',
            '/admin/settings'
        ]
    },
    
    // 文章配置
    ARTICLE: {
        EXCERPT_LENGTH: 200,
        RELATED_ARTICLES_COUNT: 4,
        POPULAR_ARTICLES_COUNT: 5,
        RECENT_ARTICLES_COUNT: 10
    },
    
    // 评论配置
    COMMENT: {
        MAX_DEPTH: 3,
        PAGE_SIZE: 20,
        MIN_LENGTH: 10,
        MAX_LENGTH: 1000
    },
    
    // 搜索配置
    SEARCH: {
        MIN_QUERY_LENGTH: 2,
        MAX_RESULTS: 50,
        HIGHLIGHT_CLASS: 'search-highlight'
    },
    
    // 图表配置
    CHARTS: {
        COLORS: {
            PRIMARY: '#007AFF',
            SECONDARY: '#5856D6',
            SUCCESS: '#34C759',
            WARNING: '#FF9500',
            ERROR: '#FF3B30',
            INFO: '#5AC8FA'
        },
        ANIMATION: {
            DURATION: 1000,
            EASING: 'easeInOutQuart'
        }
    }
};

// API端点配置
export const API_ENDPOINTS = {
    // 认证相关
    AUTH: {
        REGISTER: '/auth/register',
        LOGIN: '/auth/login',
        ME: '/auth/me',
        LOGOUT: '/auth/logout'
    },
    
    // 文章相关
    ARTICLES: {
        LIST: '/articles',
        ALL: '/articles/all',
        DETAIL: '/articles/:id',
        CREATE: '/articles',
        UPDATE: '/articles/:id',
        DELETE: '/articles/:id'
    },
    
    // 评论相关
    COMMENTS: {
        LIST: '/articles/:articleId/comments',
        CREATE: '/articles/:articleId/comments',
        DELETE: '/articles/:articleId/comments/:id'
    },
    
    // 分类相关
    CATEGORIES: {
        LIST: '/categories',
        DETAIL: '/categories/:id',
        CREATE: '/categories',
        UPDATE: '/categories/:id',
        DELETE: '/categories/:id'
    },
    
    // 搜索相关
    SEARCH: {
        ARTICLES: '/search/articles',
        ADVANCED: '/search/advanced'
    },
    
    // 分析相关
    ANALYTICS: {
        STATS: '/analytics/stats',
        POPULAR: '/analytics/popular',
        TAGS: '/analytics/tags',
        CATEGORY_DISTRIBUTION: '/analytics/category-distribution',
        POSTS_OVER_TIME: '/analytics/posts-over-time',
        USER_ANALYTICS: '/analytics/user-analytics',
        CONTENT_ANALYSIS: '/analytics/content-analysis',
        INTERACTION_NETWORK: '/analytics/interaction-network'
    }
};

// 错误消息配置
export const ERROR_MESSAGES = {
    NETWORK: '网络连接失败，请检查网络设置',
    TIMEOUT: '请求超时，请稍后重试',
    UNAUTHORIZED: '未授权访问，请先登录',
    FORBIDDEN: '权限不足，无法访问',
    NOT_FOUND: '请求的资源不存在',
    SERVER_ERROR: '服务器内部错误，请稍后重试',
    VALIDATION: '数据验证失败，请检查输入',
    UNKNOWN: '未知错误，请稍后重试'
};

// 成功消息配置
export const SUCCESS_MESSAGES = {
    LOGIN: '登录成功',
    LOGOUT: '退出登录成功',
    REGISTER: '注册成功',
    ARTICLE_CREATED: '文章创建成功',
    ARTICLE_UPDATED: '文章更新成功',
    ARTICLE_DELETED: '文章删除成功',
    COMMENT_ADDED: '评论添加成功',
    COMMENT_DELETED: '评论删除成功',
    CATEGORY_CREATED: '分类创建成功',
    CATEGORY_UPDATED: '分类更新成功',
    CATEGORY_DELETED: '分类删除成功'
};

// 验证规则配置
export const VALIDATION_RULES = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
    SLUG: /^[a-z0-9-]+$/
};

// 主题配置
export const THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
};

// 导出默认配置
export default CONFIG;
