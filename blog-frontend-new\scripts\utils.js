import { CONFIG, VALIDATION_RULES } from './config.js';

// 工具函数类
export class Utils {
    // 日期格式化
    static formatDate(date, format = 'FULL') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const now = new Date();
        const diff = now - d;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        // 相对时间格式
        if (format === 'RELATIVE') {
            if (days === 0) {
                const hours = Math.floor(diff / (1000 * 60 * 60));
                if (hours === 0) {
                    const minutes = Math.floor(diff / (1000 * 60));
                    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
                }
                return `${hours}小时前`;
            } else if (days === 1) {
                return '昨天';
            } else if (days < 7) {
                return `${days}天前`;
            } else if (days < 30) {
                const weeks = Math.floor(days / 7);
                return `${weeks}周前`;
            } else if (days < 365) {
                const months = Math.floor(days / 30);
                return `${months}个月前`;
            } else {
                const years = Math.floor(days / 365);
                return `${years}年前`;
            }
        }
        
        // 短格式
        if (format === 'SHORT') {
            return `${d.getMonth() + 1}月${d.getDate()}日`;
        }
        
        // 完整格式
        return `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日`;
    }
    
    // 数字格式化
    static formatNumber(num) {
        if (num < 1000) return num.toString();
        if (num < 10000) return (num / 1000).toFixed(1) + 'K';
        if (num < 1000000) return (num / 10000).toFixed(1) + 'W';
        return (num / 1000000).toFixed(1) + 'M';
    }
    
    // 文本截断
    static truncateText(text, length = 100, suffix = '...') {
        if (!text || text.length <= length) return text;
        return text.substring(0, length) + suffix;
    }
    
    // HTML转义
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // HTML反转义
    static unescapeHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }
    
    // 生成随机ID
    static generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 防抖函数
    static debounce(func, wait = CONFIG.UI.DEBOUNCE_DELAY) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 节流函数
    static throttle(func, limit = 100) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    // 深拷贝
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }
    
    // 对象合并
    static merge(target, ...sources) {
        if (!sources.length) return target;
        const source = sources.shift();
        
        if (this.isObject(target) && this.isObject(source)) {
            for (const key in source) {
                if (this.isObject(source[key])) {
                    if (!target[key]) Object.assign(target, { [key]: {} });
                    this.merge(target[key], source[key]);
                } else {
                    Object.assign(target, { [key]: source[key] });
                }
            }
        }
        
        return this.merge(target, ...sources);
    }
    
    // 判断是否为对象
    static isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }
    
    // 验证邮箱
    static validateEmail(email) {
        return VALIDATION_RULES.EMAIL.test(email);
    }
    
    // 验证密码
    static validatePassword(password) {
        return VALIDATION_RULES.PASSWORD.test(password);
    }
    
    // 验证用户名
    static validateUsername(username) {
        return VALIDATION_RULES.USERNAME.test(username);
    }
    
    // URL参数解析
    static parseUrlParams(url = window.location.href) {
        const params = {};
        const urlObj = new URL(url);
        urlObj.searchParams.forEach((value, key) => {
            params[key] = value;
        });
        return params;
    }
    
    // 构建URL参数
    static buildUrlParams(params) {
        const searchParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                searchParams.append(key, params[key]);
            }
        });
        return searchParams.toString();
    }
    
    // 本地存储操作
    static storage = {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Storage set error:', error);
                return false;
            }
        },
        
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Storage get error:', error);
                return defaultValue;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Storage remove error:', error);
                return false;
            }
        },
        
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('Storage clear error:', error);
                return false;
            }
        }
    };
    
    // 文件大小格式化
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 颜色工具
    static color = {
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        },
        
        rgbToHex(r, g, b) {
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        },
        
        lighten(color, amount) {
            const rgb = this.hexToRgb(color);
            if (!rgb) return color;
            
            rgb.r = Math.min(255, rgb.r + amount);
            rgb.g = Math.min(255, rgb.g + amount);
            rgb.b = Math.min(255, rgb.b + amount);
            
            return this.rgbToHex(rgb.r, rgb.g, rgb.b);
        },
        
        darken(color, amount) {
            const rgb = this.hexToRgb(color);
            if (!rgb) return color;
            
            rgb.r = Math.max(0, rgb.r - amount);
            rgb.g = Math.max(0, rgb.g - amount);
            rgb.b = Math.max(0, rgb.b - amount);
            
            return this.rgbToHex(rgb.r, rgb.g, rgb.b);
        }
    };
}

// 事件总线类
export class EventBus {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    off(event, callback) {
        if (!this.events[event]) return;
        
        if (callback) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        } else {
            delete this.events[event];
        }
    }
    
    emit(event, data) {
        if (!this.events[event]) return;
        
        this.events[event].forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Event callback error for ${event}:`, error);
            }
        });
    }
    
    once(event, callback) {
        const onceCallback = (data) => {
            callback(data);
            this.off(event, onceCallback);
        };
        this.on(event, onceCallback);
    }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 导出默认工具类
export default Utils;
