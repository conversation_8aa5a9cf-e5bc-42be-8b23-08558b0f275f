const express = require('express');
const { register, login, getMe } = require('../controllers/authController');
const { userValidationRules, loginValidationRules } = require('../middleware/validators');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Register route
router.post('/register', userValidationRules, register);

// Login route
router.post('/login', loginValidationRules, login);

// Get current user route
router.get('/me', protect, getMe);

module.exports = router; 