<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代博客系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="/" class="brand-link">
                    <i class="fas fa-blog"></i>
                    <span>现代博客</span>
                </a>
            </div>
            
            <div class="nav-menu" id="navMenu">
                <a href="/" class="nav-link active" data-route="/">首页</a>
                <a href="/categories" class="nav-link" data-route="/categories">分类</a>
                <a href="/tags" class="nav-link" data-route="/tags">标签</a>
                <a href="/about" class="nav-link" data-route="/about">关于</a>
            </div>
            
            <div class="nav-actions">
                <div class="search-container">
                    <input type="text" id="globalSearch" placeholder="搜索文章..." class="search-input">
                    <button class="search-btn" id="searchBtn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="auth-buttons" id="authButtons">
                    <button class="btn btn-outline" id="loginBtn">登录</button>
                    <a href="/admin" class="btn btn-primary">管理后台</a>
                </div>
                
                <div class="user-menu hidden" id="userMenu">
                    <div class="user-avatar" id="userAvatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="/profile" class="dropdown-item">个人资料</a>
                        <a href="/admin" class="dropdown-item">管理后台</a>
                        <button class="dropdown-item" id="logoutBtn">退出登录</button>
                    </div>
                </div>
            </div>
            
            <button class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content" id="mainContent">
        <!-- 页面内容将通过路由动态加载 -->
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>现代博客</h3>
                    <p>分享技术知识，记录成长历程</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul class="footer-links">
                        <li><a href="/">首页</a></li>
                        <li><a href="/categories">分类</a></li>
                        <li><a href="/tags">标签</a></li>
                        <li><a href="/about">关于我们</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <ul class="footer-links">
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-phone"></i> +86 123 4567 8900</li>
                        <li><i class="fas fa-map-marker-alt"></i> 中国，北京</li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 现代博客系统. 保留所有权利.</p>
                <div class="footer-bottom-links">
                    <a href="/privacy">隐私政策</a>
                    <a href="/terms">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 加载指示器 -->
    <div class="loading-overlay hidden" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- 登录模态框 -->
    <div class="modal hidden" id="loginModal">
        <div class="modal-overlay" id="loginModalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>用户登录</h2>
                <button class="modal-close" id="loginModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="loginForm" class="auth-form">
                    <div class="form-group">
                        <label for="loginEmail">邮箱</label>
                        <input type="email" id="loginEmail" name="email" required class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <input type="password" id="loginPassword" name="password" required class="form-input">
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">登录</button>
                </form>
                <div class="auth-links">
                    <p>还没有账号？ <a href="#" id="showRegister">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal hidden" id="registerModal">
        <div class="modal-overlay" id="registerModalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>用户注册</h2>
                <button class="modal-close" id="registerModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="registerForm" class="auth-form">
                    <div class="form-group">
                        <label for="registerName">用户名</label>
                        <input type="text" id="registerName" name="name" required class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="registerEmail">邮箱</label>
                        <input type="email" id="registerEmail" name="email" required class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">密码</label>
                        <input type="password" id="registerPassword" name="password" required class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required class="form-input">
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">注册</button>
                </form>
                <div class="auth-links">
                    <p>已有账号？ <a href="#" id="showLogin">立即登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script type="module" src="scripts/config.js"></script>
    <script type="module" src="scripts/utils.js"></script>
    <script type="module" src="scripts/api.js"></script>
    <script type="module" src="scripts/router.js"></script>
    <script type="module" src="scripts/auth.js"></script>
    <script type="module" src="scripts/state.js"></script>
    <script type="module" src="scripts/ui.js"></script>
    <script type="module" src="scripts/main.js"></script>
</body>
</html>
