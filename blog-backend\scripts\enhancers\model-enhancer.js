/**
 * 数据模型增强器
 * 
 * 自动扩展和优化MongoDB数据模型，利用文档结构灵活性
 */

const fs = require('fs');
const path = require('path');
const colors = require('colors');
const mongoose = require('mongoose');

// 模型文件路径
const MODELS_DIR = path.join(__dirname, '../../models');

// 模型模板
const MODEL_TEMPLATES = {
  // 增强的文章模型模板
  Article: `const mongoose = require('mongoose');

// 内容块模式
const ContentBlockSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['text', 'heading', 'image', 'code', 'quote', 'list']
  },
  content: {
    type: String,
    required: true
  },
  metadata: {
    language: String,  // 用于代码块
    caption: String,   // 用于图片
    level: Number,     // 用于标题 (1-6)
    listType: {        // 用于列表
      type: String,
      enum: ['ordered', 'unordered']
    }
  }
}, { _id: false });

// 版本记录模式
const VersionSchema = new mongoose.Schema({
  content: {
    type: [ContentBlockSchema],
    required: true
  },
  title: String,
  savedAt: {
    type: Date,
    default: Date.now
  },
  savedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, { _id: false });

// 增强的文章模式
const ArticleSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Please provide a title'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters']
    },
    // 结构化内容块
    contentBlocks: {
      type: [ContentBlockSchema],
      default: []
    },
    // 保留兼容性
    content: {
      type: String,
      required: function() {
        return this.contentBlocks.length === 0;
      },
      minlength: [10, 'Content should be at least 10 characters long']
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    slug: {
      type: String,
      unique: true,
      sparse: true
    },
    tags: {
      type: [String],
      default: []
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: false
    },
    viewCount: {
      type: Number,
      default: 0
    },
    // 扩展状态选项
    status: {
      type: String,
      enum: ['draft', 'published', 'archived', 'scheduled'],
      default: 'draft'
    },
    isPublished: {
      type: Boolean,
      default: false
    },
    // 扩展SEO字段
    seo: {
      metaTitle: String,
      metaDescription: String,
      canonical: String,
      keywords: [String]
    },
    // 版本历史
    versions: {
      type: [VersionSchema],
      default: []
    },
    // 动态元数据 - 允许任意的键值对
    metadata: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      default: {}
    },
    scheduledPublishDate: Date,
    // 特色图片
    featuredImage: {
      url: String,
      alt: String,
      caption: String
    },
    // 阅读时长估计(分钟)
    readTime: Number
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// 虚拟字段 - 评论
ArticleSchema.virtual('comments', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'article',
  justOne: false
});

// 索引配置
ArticleSchema.index({ title: 'text', 'contentBlocks.content': 'text' });
ArticleSchema.index({ tags: 1 });
ArticleSchema.index({ category: 1 });
ArticleSchema.index({ status: 1, createdAt: -1 });
ArticleSchema.index({ slug: 1 }, { unique: true, sparse: true });
ArticleSchema.index({ 'metadata.customField': 1 }); // 示例自定义字段索引
ArticleSchema.index({ scheduledPublishDate: 1 }, { sparse: true });

// 中间件 - 保存前处理
ArticleSchema.pre('save', function(next) {
  // 更新阅读时间估计
  if (this.contentBlocks && this.contentBlocks.length > 0) {
    const wordCount = this.contentBlocks
      .filter(block => block.type === 'text')
      .reduce((count, block) => count + block.content.split(/\\s+/).length, 0);
    
    // 假设平均阅读速度为每分钟200字
    this.readTime = Math.ceil(wordCount / 200);
  } else if (this.content) {
    const wordCount = this.content.split(/\\s+/).length;
    this.readTime = Math.ceil(wordCount / 200);
  }
  
  // 状态和isPublished同步
  this.isPublished = this.status === 'published';
  
  // 创建版本记录
  if (this.isModified('contentBlocks') || this.isModified('content') || this.isModified('title')) {
    const versionData = {
      title: this.title,
      savedAt: new Date(),
      savedBy: this.author
    };
    
    if (this.contentBlocks && this.contentBlocks.length > 0) {
      versionData.content = this.contentBlocks;
    } else {
      versionData.content = [{ type: 'text', content: this.content }];
    }
    
    this.versions.push(versionData);
    
    // 限制版本历史数量
    const MAX_VERSIONS = 10;
    if (this.versions.length > MAX_VERSIONS) {
      this.versions = this.versions.slice(this.versions.length - MAX_VERSIONS);
    }
  }
  
  next();
});

module.exports = mongoose.model('Article', ArticleSchema);
`,

  // 增强的评论模型模板
  Comment: `const mongoose = require('mongoose');

const CommentSchema = new mongoose.Schema(
  {
    content: {
      type: String,
      required: [true, 'Please provide comment content'],
      trim: true,
      maxlength: [1000, 'Comment cannot be more than 1000 characters']
    },
    article: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Article',
      required: true
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    // 支持嵌套评论
    parentComment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment',
      default: null
    },
    // 点赞数
    likes: {
      type: Number,
      default: 0
    },
    // 点赞用户
    likedBy: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    // 回复评论
    replies: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment'
    }],
    // 状态 (审核功能)
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'spam'],
      default: 'approved'
    },
    // 评论元数据
    metadata: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      default: {}
    },
    // IP地址和用户代理
    ipAddress: String,
    userAgent: String
  },
  { timestamps: true }
);

// 索引
CommentSchema.index({ article: 1, createdAt: -1 });
CommentSchema.index({ parentComment: 1 });
CommentSchema.index({ author: 1 });
CommentSchema.index({ status: 1 });

// 中间件 - 保存后处理
CommentSchema.post('save', async function(doc) {
  // 如果这是回复评论，更新父评论的回复列表
  if (doc.parentComment) {
    await mongoose.model('Comment').findByIdAndUpdate(
      doc.parentComment,
      { $addToSet: { replies: doc._id } }
    );
  }
});

module.exports = mongoose.model('Comment', CommentSchema);
`
};

/**
 * 增强数据模型
 * @param {Object} options - 增强选项
 * @param {boolean} dryRun - 仅显示不执行
 */
async function enhance(options, dryRun = false) {
  console.log(colors.cyan('\n=== 开始增强数据模型 ==='));
  
  try {
    // 检查模型目录
    if (!fs.existsSync(MODELS_DIR)) {
      console.error(colors.red('✗ 模型目录不存在:'), MODELS_DIR);
      return;
    }
    
    // 显示配置
    console.log(colors.yellow('应用的增强:'));
    Object.entries(options).forEach(([key, value]) => {
      console.log(`  ${key}: ${value ? colors.green('启用') : colors.red('禁用')}`);
    });
    
    // 仅在非演示模式下执行
    if (!dryRun) {
      // 备份现有模型
      await backupModels();
      
      // 更新模型文件
      await updateModels();
    } else {
      console.log(colors.yellow('\n[演示模式] 将更新以下模型文件:'));
      console.log('  - Article.js');
      console.log('  - Comment.js');
    }
    
    console.log(colors.green('\n✓ 数据模型增强完成!'));
  } catch (error) {
    console.error(colors.red('✗ 模型增强失败:'), error);
    throw error;
  }
}

/**
 * 备份现有模型文件
 */
async function backupModels() {
  console.log(colors.cyan('\n正在备份现有模型...'));
  
  const backupDir = path.join(MODELS_DIR, '../models-backup');
  
  // 创建备份目录
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir);
  }
  
  // 备份需要修改的模型
  for (const model of Object.keys(MODEL_TEMPLATES)) {
    const modelFile = path.join(MODELS_DIR, `${model}.js`);
    if (fs.existsSync(modelFile)) {
      const backupFile = path.join(backupDir, `${model}.js.bak.${Date.now()}`);
      fs.copyFileSync(modelFile, backupFile);
      console.log(colors.green(`✓ 已备份 ${model}.js`));
    }
  }
}

/**
 * 更新模型文件
 */
async function updateModels() {
  console.log(colors.cyan('\n正在更新模型文件...'));
  
  // 更新每个模型
  for (const [model, template] of Object.entries(MODEL_TEMPLATES)) {
    const modelFile = path.join(MODELS_DIR, `${model}.js`);
    fs.writeFileSync(modelFile, template);
    console.log(colors.green(`✓ 已更新 ${model}.js`));
  }
}

module.exports = {
  enhance
}; 