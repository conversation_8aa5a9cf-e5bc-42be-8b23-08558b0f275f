import { CONFIG } from './config.js';
import { api } from './api.js';
import { appState } from './state.js';
import { router } from './router.js';
import { authManager } from './auth.js';
import { uiManager } from './ui.js';
import { Utils, eventBus } from './utils.js';

// 管理后台应用类
class AdminApp {
    constructor() {
        this.initialized = false;
        this.charts = new Map();
    }
    
    // 初始化管理后台
    async init() {
        try {
            console.log('Initializing Admin App...');
            
            // 设置全局错误处理
            this.setupErrorHandling();
            
            // 检查管理员权限
            await this.checkAdminAccess();
            
            // 初始化认证状态
            await authManager.init();
            
            // 初始化路由
            router.init();
            
            // 初始化UI管理器
            uiManager.init();
            
            // 设置管理后台特定的事件监听器
            this.setupAdminEventListeners();
            
            // 加载管理后台初始数据
            await this.loadAdminData();
            
            // 标记为已初始化
            this.initialized = true;
            
            console.log('Admin App initialized successfully');
            
            // 发送管理后台初始化完成事件
            eventBus.emit('adminAppInitialized');
            
        } catch (error) {
            console.error('Admin app initialization error:', error);
            this.handleInitError(error);
        }
    }
    
    // 检查管理员访问权限
    async checkAdminAccess() {
        const state = appState.getState();
        
        // 如果不是登录页面，检查认证状态
        if (window.location.pathname !== '/admin/login') {
            if (!state.isAuthenticated || !state.isAdmin) {
                // 显示管理员登录模态框
                const adminLoginModal = document.getElementById('adminLoginModal');
                if (adminLoginModal) {
                    adminLoginModal.classList.remove('hidden');
                }
                throw new Error('需要管理员权限');
            }
        }
    }
    
    // 设置全局错误处理
    setupErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            appState.addToast({
                type: 'error',
                message: '发生了一个错误，请刷新页面重试'
            });
        });
        
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('JavaScript error:', event.error);
            appState.addToast({
                type: 'error',
                message: '页面出现错误，请刷新页面重试'
            });
        });
    }
    
    // 加载管理后台数据
    async loadAdminData() {
        try {
            // 并行加载管理后台基础数据
            const promises = [
                this.loadDashboardStats(),
                this.loadCategories(),
                this.loadRecentArticles()
            ];
            
            await Promise.allSettled(promises);
            
        } catch (error) {
            console.error('Failed to load admin data:', error);
        }
    }
    
    // 加载仪表板统计数据
    async loadDashboardStats() {
        try {
            const stats = await api.getStats();
            appState.setState({ dashboardStats: stats });
        } catch (error) {
            console.error('Failed to load dashboard stats:', error);
        }
    }
    
    // 加载分类数据
    async loadCategories() {
        try {
            const categories = await api.getCategories();
            appState.setCategories(categories);
        } catch (error) {
            console.error('Failed to load categories:', error);
        }
    }
    
    // 加载最近文章
    async loadRecentArticles() {
        try {
            const articles = await api.getAllArticles({ limit: 10, sort: '-createdAt' });
            appState.setState({ recentArticles: articles.articles || articles });
        } catch (error) {
            console.error('Failed to load recent articles:', error);
        }
    }
    
    // 设置管理后台事件监听器
    setupAdminEventListeners() {
        // 监听路由变化
        eventBus.on('routeChange', this.handleAdminRouteChange.bind(this));
        
        // 监听状态变化
        appState.subscribe(this.handleAdminStateChange.bind(this));
        
        // 监听数据刷新事件
        eventBus.on('refreshAdminData', this.refreshAdminData.bind(this));
        
        // 监听主题变化
        eventBus.on('themeChanged', this.handleThemeChange.bind(this));
        
        // 设置管理后台特定的DOM事件
        this.setupAdminDOMEvents();
    }
    
    // 设置管理后台DOM事件
    setupAdminDOMEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.handleRefresh.bind(this));
        }
        
        // 通知按钮
        const notificationBtn = document.getElementById('notificationBtn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', this.handleNotifications.bind(this));
        }
        
        // 侧边栏导航
        document.addEventListener('click', this.handleSidebarNavigation.bind(this));
    }
    
    // 处理管理后台路由变化
    handleAdminRouteChange(data) {
        const { route, params } = data;
        
        // 更新侧边栏活动状态
        this.updateSidebarActive(route.path);
        
        // 更新页面标题
        this.updateAdminPageTitle(route.component);
        
        // 根据路由加载相应数据
        this.loadRouteData(route.component, params);
    }
    
    // 更新侧边栏活动状态
    updateSidebarActive(path) {
        document.querySelectorAll('.sidebar-nav .nav-link').forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href') || link.getAttribute('data-route');
            if (href === path) {
                link.classList.add('active');
            }
        });
    }
    
    // 更新管理后台页面标题
    updateAdminPageTitle(component) {
        const pageTitle = document.getElementById('pageTitle');
        if (!pageTitle) return;
        
        let title = '仪表板';
        
        switch (component) {
            case 'admin-dashboard':
                title = '仪表板';
                break;
            case 'admin-articles':
                title = '文章管理';
                break;
            case 'admin-article-editor':
                title = '文章编辑';
                break;
            case 'admin-categories':
                title = '分类管理';
                break;
            case 'admin-comments':
                title = '评论管理';
                break;
            case 'admin-users':
                title = '用户管理';
                break;
            case 'admin-settings':
                title = '系统设置';
                break;
        }
        
        pageTitle.textContent = title;
    }
    
    // 根据路由加载数据
    async loadRouteData(component, params) {
        try {
            switch (component) {
                case 'admin-dashboard':
                    await this.loadDashboardData();
                    break;
                case 'admin-articles':
                    await this.loadArticlesData();
                    break;
                case 'admin-article-editor':
                    if (params.id) {
                        await this.loadArticleForEdit(params.id);
                    }
                    break;
                case 'admin-categories':
                    await this.loadCategoriesData();
                    break;
                case 'admin-comments':
                    await this.loadCommentsData();
                    break;
            }
        } catch (error) {
            console.error('Failed to load route data:', error);
        }
    }
    
    // 加载仪表板数据
    async loadDashboardData() {
        try {
            const [stats, categoryDistribution, postsOverTime, popularPosts] = await Promise.all([
                api.getStats(),
                api.getCategoryDistribution(),
                api.getPostsOverTime(),
                api.getPopularPosts()
            ]);
            
            appState.setState({
                dashboardStats: stats,
                categoryDistribution,
                postsOverTime,
                popularPosts
            });
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }
    
    // 加载文章数据
    async loadArticlesData() {
        try {
            const articles = await api.getAllArticles();
            appState.setState({ adminArticles: articles });
        } catch (error) {
            console.error('Failed to load articles data:', error);
        }
    }
    
    // 加载要编辑的文章
    async loadArticleForEdit(articleId) {
        try {
            const article = await api.getArticle(articleId);
            appState.setCurrentArticle(article);
        } catch (error) {
            console.error('Failed to load article for edit:', error);
        }
    }
    
    // 加载分类数据
    async loadCategoriesData() {
        try {
            const categories = await api.getCategories();
            appState.setCategories(categories);
        } catch (error) {
            console.error('Failed to load categories data:', error);
        }
    }
    
    // 加载评论数据
    async loadCommentsData() {
        try {
            // 这里需要实现获取所有评论的API
            // const comments = await api.getAllComments();
            // appState.setState({ adminComments: comments });
        } catch (error) {
            console.error('Failed to load comments data:', error);
        }
    }
    
    // 处理管理后台状态变化
    handleAdminStateChange(currentState, prevState) {
        // 处理认证状态变化
        if (currentState.isAuthenticated !== prevState.isAuthenticated) {
            if (!currentState.isAuthenticated) {
                // 用户登出，跳转到登录页
                router.navigate('/admin/login');
            }
        }
        
        // 处理管理员权限变化
        if (currentState.isAdmin !== prevState.isAdmin) {
            if (!currentState.isAdmin) {
                // 失去管理员权限，跳转到首页
                appState.addToast({
                    type: 'error',
                    message: '权限不足，已退出管理后台'
                });
                router.navigate('/');
            }
        }
    }
    
    // 刷新管理后台数据
    async refreshAdminData() {
        try {
            appState.setLoading(true, '刷新数据中...');
            await this.loadAdminData();
            
            appState.addToast({
                type: 'success',
                message: '数据刷新成功'
            });
        } catch (error) {
            console.error('Failed to refresh admin data:', error);
            appState.addToast({
                type: 'error',
                message: '数据刷新失败'
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理刷新按钮点击
    handleRefresh() {
        this.refreshAdminData();
    }
    
    // 处理通知按钮点击
    handleNotifications() {
        // 实现通知功能
        appState.addToast({
            type: 'info',
            message: '暂无新通知'
        });
    }
    
    // 处理侧边栏导航点击
    handleSidebarNavigation(event) {
        const link = event.target.closest('.nav-link');
        if (!link) return;
        
        const route = link.getAttribute('data-route') || link.getAttribute('href');
        if (route && route.startsWith('/admin')) {
            event.preventDefault();
            router.navigate(route);
        }
    }
    
    // 处理主题变化
    handleThemeChange(theme) {
        // 更新图表主题
        this.updateChartsTheme(theme);
    }
    
    // 更新图表主题
    updateChartsTheme(theme) {
        // 如果有图表实例，更新它们的主题
        this.charts.forEach(chart => {
            if (chart && typeof chart.update === 'function') {
                // 更新图表配置
                chart.update();
            }
        });
    }
    
    // 创建图表
    createChart(canvasId, config) {
        try {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return null;
            
            const chart = new Chart(canvas, config);
            this.charts.set(canvasId, chart);
            
            return chart;
        } catch (error) {
            console.error('Failed to create chart:', error);
            return null;
        }
    }
    
    // 销毁图表
    destroyChart(canvasId) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.destroy();
            this.charts.delete(canvasId);
        }
    }
    
    // 处理初始化错误
    handleInitError(error) {
        // 如果是权限错误，显示登录界面
        if (error.message.includes('权限')) {
            const adminLoginModal = document.getElementById('adminLoginModal');
            if (adminLoginModal) {
                adminLoginModal.classList.remove('hidden');
            }
            return;
        }
        
        // 其他错误显示错误页面
        const adminContent = document.getElementById('adminContent');
        if (adminContent) {
            adminContent.innerHTML = `
                <div class="init-error">
                    <div class="error-content">
                        <h1>管理后台初始化失败</h1>
                        <p>抱歉，管理后台无法正常启动。</p>
                        <p class="error-message">${error.message}</p>
                        <button onclick="location.reload()" class="btn btn-primary">重新加载</button>
                    </div>
                </div>
            `;
        }
    }
    
    // 获取管理后台信息
    getAdminInfo() {
        return {
            version: '1.0.0',
            initialized: this.initialized,
            currentRoute: router.getCurrentRoute(),
            state: appState.getState(),
            charts: Array.from(this.charts.keys())
        };
    }
    
    // 销毁管理后台
    destroy() {
        // 销毁所有图表
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
        
        // 清理事件监听器
        eventBus.off('routeChange');
        eventBus.off('refreshAdminData');
        eventBus.off('themeChanged');
        
        // 标记为未初始化
        this.initialized = false;
        
        console.log('Admin App destroyed');
    }
}

// 创建管理后台应用实例
const adminApp = new AdminApp();

// DOM加载完成后初始化管理后台
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        adminApp.init();
    });
} else {
    adminApp.init();
}

// 导出管理后台应用实例供调试使用
window.AdminApp = adminApp;

// 导出管理后台应用类
export default AdminApp;
