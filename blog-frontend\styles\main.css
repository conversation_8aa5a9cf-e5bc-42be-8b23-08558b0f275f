/* ========== 基础样式和变量 ========== */
:root {
    /* iOS风格颜色系统 */
    --primary-color: #007AFF;
    --primary-dark: #0056CC;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    
    /* 中性色系 */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #FFFFFF;
    --surface-color: rgba(255, 255, 255, 0.8);
    --overlay-color: rgba(0, 0, 0, 0.4);
    
    /* 阴影系统 */
    --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.15);
    
    /* 圆角系统 */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 24px;
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;
    
    /* 字体系统 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-size-4xl: 40px;
    
    /* 动画 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.25s ease-out;
    --transition-slow: 0.35s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #FFFFFF;
        --text-secondary: #98989D;
        --text-tertiary: #636366;
        --background-primary: #000000;
        --background-secondary: #1C1C1E;
        --background-tertiary: #2C2C2E;
        --surface-color: rgba(28, 28, 30, 0.8);
        --overlay-color: rgba(0, 0, 0, 0.6);
    }
}

/* ========== 基础重置 ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ========== 通用组件 ========== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
}

/* 按钮系统 */
.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-large);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-small);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-large);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.btn-secondary:hover {
    background: var(--background-tertiary);
    border-color: var(--text-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-small);
}

/* 卡片组件 */
.card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

/* 模糊效果 */
.blur-background {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* ========== 导航栏 ========== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--surface-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(0, 122, 255, 0.1);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.btn-search,
.btn-auth {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--background-secondary);
    border: none;
    border-radius: var(--radius-medium);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-search:hover,
.btn-auth:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.nav-toggle span {
    width: 20px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all var(--transition-fast);
}

/* 移动端导航 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--surface-color);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        flex-direction: column;
        padding: var(--spacing-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* ========== 搜索覆盖层 ========== */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--overlay-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 2000;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 20vh;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.search-container {
    width: 100%;
    max-width: 600px;
    padding: 0 var(--spacing-lg);
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-large);
    margin-bottom: var(--spacing-lg);
}

.search-box i {
    color: var(--text-secondary);
    margin-right: var(--spacing-md);
}

.search-box input {
    flex: 1;
    border: none;
    background: none;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    outline: none;
}

.search-box input::placeholder {
    color: var(--text-secondary);
}

.search-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
}

.search-close:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.search-suggestions {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-medium);
    max-height: 400px;
    overflow-y: auto;
}

/* ========== 英雄区域 ========== */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

.hero-content {
    color: white;
    max-width: 800px;
    padding: 0 var(--spacing-lg);
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    font-weight: 400;
    margin-bottom: var(--spacing-2xl);
    opacity: 0.9;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: var(--font-size-lg);
    animation: bounce 2s infinite;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* ========== 动画 ========== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* ========== 内容区域 ========== */
.main-content {
    padding-top: 80px; /* 为固定导航栏留出空间 */
}

/* 区域标题 */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ========== 特色文章区域 ========== */
.featured-section {
    padding: var(--spacing-3xl) 0;
    background: var(--background-secondary);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.featured-article {
    position: relative;
    height: 300px;
    border-radius: var(--radius-large);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-medium);
}

.featured-article:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
}

.featured-article-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    z-index: -1;
}

.featured-article-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
}

.featured-article-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-xl);
    color: white;
}

.featured-article-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.featured-article-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.featured-article-excerpt {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    line-height: 1.5;
}

/* ========== 文章列表区域 ========== */
.articles-section {
    padding: var(--spacing-3xl) 0;
}

.section-filters {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
}

.article-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-small);
    transition: all var(--transition-normal);
    cursor: pointer;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.article-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.article-card-header {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: relative;
    overflow: hidden;
}

.article-card-category {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.article-card-content {
    padding: var(--spacing-lg);
}

.article-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-card-excerpt {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.article-card-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.article-card-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.article-card-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* ========== 分页 ========== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--text-tertiary);
    background: var(--surface-color);
    color: var(--text-secondary);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    font-size: var(--font-size-sm);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.pagination-btn:hover,
.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ========== 分类区域 ========== */
.categories-section {
    padding: var(--spacing-3xl) 0;
    background: var(--background-secondary);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.category-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.category-card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: white;
    font-size: var(--font-size-xl);
}

.category-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.category-card-count {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.category-card-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* ========== 关于区域 ========== */
.about-section {
    padding: var(--spacing-3xl) 0;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-text {
    max-width: 600px;
}

.about-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
}

.about-features {
    display: grid;
    gap: var(--spacing-lg);
}

.feature-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.feature-item i {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-base);
    flex-shrink: 0;
}

.feature-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.feature-item p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin: 0;
}

.about-stats {
    display: grid;
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--surface-color);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
}

/* ========== 页脚 ========== */
.footer {
    background: var(--background-secondary);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
    margin-top: var(--spacing-3xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--surface-color);
    color: var(--text-secondary);
    border-radius: var(--radius-medium);
    text-decoration: none;
    transition: all var(--transition-fast);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--text-tertiary);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .footer-links {
        flex-wrap: wrap;
    }

    .footer-social {
        justify-content: center;
    }
}

/* ========== 模态框 ========== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--overlay-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-large);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

.article-modal-content {
    max-width: 800px;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-tertiary);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--background-secondary);
    border: none;
    border-radius: var(--radius-small);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--error-color);
    color: white;
}

.modal-body {
    padding: var(--spacing-lg);
}

/* ========== 加载指示器 ========== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--overlay-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 4000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    margin: 0 auto var(--spacing-md);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========== 表单样式 ========== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-input::placeholder {
    color: var(--text-secondary);
}

/* ========== 响应式设计 ========== */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .featured-grid,
    .articles-grid,
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: var(--font-size-2xl);
    }

    .section-filters {
        flex-direction: column;
        align-items: center;
    }

    .featured-grid,
    .articles-grid,
    .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .hero-section {
        height: 80vh;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .modal-content {
        margin: var(--spacing-md);
        max-width: none;
    }
}

/* ========== 博客布局 ========== */
.blog-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    align-items: start;
}

.blog-main {
    min-width: 0; /* 防止内容溢出 */
}

.blog-sidebar {
    position: sticky;
    top: 100px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    padding: 0;
}

/* ========== 侧边栏组件 ========== */
.sidebar-widget {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.sidebar-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.widget-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color);
}

/* 搜索组件 */
.search-widget {
    display: flex;
    gap: 0.5rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-btn {
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 热门文章 */
.popular-articles {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.popular-article {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
    cursor: pointer;
}

.popular-article:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(4px);
}

.popular-article-index {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-size: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.popular-article-content {
    flex: 1;
    min-width: 0;
}

.popular-article-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.popular-article-meta {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: flex;
    gap: 0.5rem;
}

/* 标签云 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-cloud-item {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.8rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tag-cloud-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.tag-cloud-item.large {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.tag-cloud-item.medium {
    font-size: 0.85rem;
    padding: 0.45rem 0.9rem;
}

/* 最新评论 */
.recent-comments {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recent-comment {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    border-left: 3px solid var(--accent-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.recent-comment:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(2px);
}

.recent-comment-author {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.recent-comment-content {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.recent-comment-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 归档列表 */
.archive-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.archive-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    cursor: pointer;
}

.archive-item:hover {
    background: rgba(255, 255, 255, 0.8);
    color: var(--primary-color);
    transform: translateX(2px);
}

.archive-month {
    font-size: 0.9rem;
    font-weight: 500;
}

.archive-count {
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: rgba(0, 122, 255, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
}

/* 响应式侧边栏 */
@media (max-width: 1024px) {
    .blog-layout {
        grid-template-columns: 1fr 250px;
        gap: 1.5rem;
    }

    .blog-sidebar {
        top: 90px;
    }
}

@media (max-width: 768px) {
    /* 移动端隐藏侧边栏，改为底部显示 */
    .blog-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .blog-sidebar {
        position: static;
        max-height: none;
        order: 2;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .sidebar-widget {
        margin-bottom: 0;
    }
}

@media (max-width: 480px) {
    .blog-sidebar {
        grid-template-columns: 1fr;
    }
}
