import { api } from '../../api.js';
import { appState } from '../../state.js';
import { CONFIG } from '../../config.js';
import { Utils } from '../../utils.js';

// 管理后台仪表板组件
export class AdminDashboard {
    constructor() {
        this.stats = null;
        this.charts = new Map();
        this.refreshInterval = null;
    }
    
    // 渲染仪表板
    async render(params = {}, query = {}) {
        try {
            // 显示加载状态
            appState.setLoading(true, '加载仪表板数据...');
            
            // 加载仪表板数据
            await this.loadDashboardData();
            
            // 渲染页面内容
            this.renderContent();
            
            // 初始化图表
            this.initCharts();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 设置自动刷新
            this.setupAutoRefresh();
            
        } catch (error) {
            console.error('Dashboard render error:', error);
            this.renderError(error);
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 加载仪表板数据
    async loadDashboardData() {
        try {
            const [stats, categoryDistribution, postsOverTime, popularPosts, recentComments] = await Promise.all([
                api.getStats(),
                api.getCategoryDistribution(),
                api.getPostsOverTime(),
                api.getPopularPosts(),
                api.getRecentComments()
            ]);
            
            this.stats = stats;
            this.categoryDistribution = categoryDistribution || [];
            this.postsOverTime = postsOverTime || [];
            this.popularPosts = popularPosts || [];
            this.recentComments = recentComments || [];
            
            // 更新应用状态
            appState.setState({
                dashboardStats: stats,
                categoryDistribution: this.categoryDistribution,
                postsOverTime: this.postsOverTime,
                popularPosts: this.popularPosts,
                recentComments: this.recentComments
            });
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            throw error;
        }
    }
    
    // 渲染页面内容
    renderContent() {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;
        
        adminContent.innerHTML = `
            <div class="dashboard-page">
                <div class="dashboard-header">
                    <h1>仪表板</h1>
                    <div class="dashboard-actions">
                        <button class="btn btn-secondary" id="refreshDashboard">
                            <i class="fas fa-sync-alt"></i>
                            刷新数据
                        </button>
                        <button class="btn btn-primary" id="newArticle">
                            <i class="fas fa-plus"></i>
                            新建文章
                        </button>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                ${this.renderStatsCards()}
                
                <!-- 图表区域 -->
                <div class="dashboard-charts">
                    <div class="row">
                        <div class="col-lg-8 col-md-12">
                            ${this.renderPostsChart()}
                        </div>
                        <div class="col-lg-4 col-md-12">
                            ${this.renderCategoryChart()}
                        </div>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="dashboard-tables">
                    <div class="row">
                        <div class="col-lg-6 col-md-12">
                            ${this.renderPopularPosts()}
                        </div>
                        <div class="col-lg-6 col-md-12">
                            ${this.renderRecentComments()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染统计卡片
    renderStatsCards() {
        if (!this.stats) return '';
        
        const cards = [
            {
                title: '总文章数',
                value: this.stats.totalArticles || 0,
                icon: 'fas fa-file-alt',
                color: 'primary',
                change: this.stats.articlesChange || 0
            },
            {
                title: '总评论数',
                value: this.stats.totalComments || 0,
                icon: 'fas fa-comments',
                color: 'success',
                change: this.stats.commentsChange || 0
            },
            {
                title: '总浏览量',
                value: this.stats.totalViews || 0,
                icon: 'fas fa-eye',
                color: 'info',
                change: this.stats.viewsChange || 0
            },
            {
                title: '用户数量',
                value: this.stats.totalUsers || 0,
                icon: 'fas fa-users',
                color: 'warning',
                change: this.stats.usersChange || 0
            }
        ];
        
        return `
            <div class="stats-cards">
                <div class="row">
                    ${cards.map(card => `
                        <div class="col-lg-3 col-md-6 col-sm-12">
                            <div class="stats-card stats-card-${card.color}">
                                <div class="stats-icon">
                                    <i class="${card.icon}"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-value">${Utils.formatNumber(card.value)}</div>
                                    <div class="stats-title">${card.title}</div>
                                    <div class="stats-change ${card.change >= 0 ? 'positive' : 'negative'}">
                                        <i class="fas fa-arrow-${card.change >= 0 ? 'up' : 'down'}"></i>
                                        ${Math.abs(card.change)}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    // 渲染文章发布趋势图表
    renderPostsChart() {
        return `
            <div class="chart-card">
                <div class="chart-header">
                    <h3>文章发布趋势</h3>
                    <div class="chart-actions">
                        <select id="postsChartPeriod" class="form-select">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                        </select>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="postsChart" width="400" height="200"></canvas>
                </div>
            </div>
        `;
    }
    
    // 渲染分类分布图表
    renderCategoryChart() {
        return `
            <div class="chart-card">
                <div class="chart-header">
                    <h3>分类分布</h3>
                </div>
                <div class="chart-body">
                    <canvas id="categoryChart" width="300" height="300"></canvas>
                </div>
            </div>
        `;
    }
    
    // 渲染热门文章表格
    renderPopularPosts() {
        return `
            <div class="table-card">
                <div class="table-header">
                    <h3>热门文章</h3>
                    <a href="/admin/articles" class="btn btn-sm btn-outline-primary" data-route="/admin/articles">
                        查看全部
                    </a>
                </div>
                <div class="table-body">
                    ${this.popularPosts && this.popularPosts.length > 0 ? `
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>标题</th>
                                        <th>浏览量</th>
                                        <th>评论数</th>
                                        <th>发布时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${this.popularPosts.slice(0, 5).map(post => `
                                        <tr>
                                            <td>
                                                <a href="/admin/articles/edit/${post._id}" class="table-link" data-route="/admin/articles/edit/${post._id}">
                                                    ${post.title}
                                                </a>
                                            </td>
                                            <td>${Utils.formatNumber(post.views || 0)}</td>
                                            <td>${Utils.formatNumber(post.commentCount || 0)}</td>
                                            <td>${Utils.formatDate(post.createdAt, 'SHORT')}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : `
                        <div class="no-data">
                            <i class="fas fa-chart-line"></i>
                            <p>暂无热门文章数据</p>
                        </div>
                    `}
                </div>
            </div>
        `;
    }
    
    // 渲染最近评论表格
    renderRecentComments() {
        return `
            <div class="table-card">
                <div class="table-header">
                    <h3>最近评论</h3>
                    <a href="/admin/comments" class="btn btn-sm btn-outline-primary" data-route="/admin/comments">
                        查看全部
                    </a>
                </div>
                <div class="table-body">
                    ${this.recentComments && this.recentComments.length > 0 ? `
                        <div class="comments-list">
                            ${this.recentComments.slice(0, 5).map(comment => `
                                <div class="comment-item">
                                    <div class="comment-avatar">
                                        <img src="${comment.author?.avatar || '/images/default-avatar.png'}" alt="${comment.author?.name || '匿名'}">
                                    </div>
                                    <div class="comment-content">
                                        <div class="comment-header">
                                            <span class="comment-author">${comment.author?.name || '匿名'}</span>
                                            <span class="comment-date">${Utils.formatDate(comment.createdAt, 'RELATIVE')}</span>
                                        </div>
                                        <div class="comment-text">${comment.content}</div>
                                        <div class="comment-article">
                                            文章：<a href="/admin/articles/edit/${comment.article?._id}" class="table-link" data-route="/admin/articles/edit/${comment.article?._id}">
                                                ${comment.article?.title || '未知文章'}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : `
                        <div class="no-data">
                            <i class="fas fa-comments"></i>
                            <p>暂无最近评论</p>
                        </div>
                    `}
                </div>
            </div>
        `;
    }
    
    // 初始化图表
    initCharts() {
        this.initPostsChart();
        this.initCategoryChart();
    }
    
    // 初始化文章发布趋势图表
    initPostsChart() {
        const canvas = document.getElementById('postsChart');
        if (!canvas || !this.postsOverTime) return;
        
        const ctx = canvas.getContext('2d');
        
        // 准备数据
        const labels = this.postsOverTime.map(item => Utils.formatDate(item.date, 'SHORT'));
        const data = this.postsOverTime.map(item => item.count);
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '文章发布数量',
                    data: data,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
        
        this.charts.set('postsChart', chart);
    }
    
    // 初始化分类分布图表
    initCategoryChart() {
        const canvas = document.getElementById('categoryChart');
        if (!canvas || !this.categoryDistribution) return;
        
        const ctx = canvas.getContext('2d');
        
        // 准备数据
        const labels = this.categoryDistribution.map(item => item.name);
        const data = this.categoryDistribution.map(item => item.count);
        const colors = [
            '#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1',
            '#fd7e14', '#20c997', '#6c757d', '#e83e8c', '#17a2b8'
        ];
        
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        this.charts.set('categoryChart', chart);
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.handleRefresh.bind(this));
        }
        
        // 新建文章按钮
        const newArticleBtn = document.getElementById('newArticle');
        if (newArticleBtn) {
            newArticleBtn.addEventListener('click', () => {
                window.location.href = '/admin/articles/new';
            });
        }
        
        // 图表时间段选择
        const periodSelect = document.getElementById('postsChartPeriod');
        if (periodSelect) {
            periodSelect.addEventListener('change', this.handlePeriodChange.bind(this));
        }
    }
    
    // 处理刷新
    async handleRefresh() {
        try {
            appState.setLoading(true, '刷新数据中...');
            
            await this.loadDashboardData();
            this.renderContent();
            this.initCharts();
            this.setupEventListeners();
            
            appState.addToast({
                type: 'success',
                message: '数据刷新成功'
            });
            
        } catch (error) {
            console.error('Refresh error:', error);
            appState.addToast({
                type: 'error',
                message: '数据刷新失败'
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理时间段变化
    async handlePeriodChange(event) {
        const period = parseInt(event.target.value);
        
        try {
            appState.setLoading(true, '更新图表数据...');
            
            const postsOverTime = await api.getPostsOverTime({ days: period });
            this.postsOverTime = postsOverTime || [];
            
            // 更新图表
            const chart = this.charts.get('postsChart');
            if (chart) {
                chart.destroy();
                this.initPostsChart();
            }
            
        } catch (error) {
            console.error('Period change error:', error);
            appState.addToast({
                type: 'error',
                message: '图表更新失败'
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 设置自动刷新
    setupAutoRefresh() {
        // 每5分钟自动刷新一次数据
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData().catch(error => {
                console.error('Auto refresh error:', error);
            });
        }, 5 * 60 * 1000);
    }
    
    // 清理资源
    destroy() {
        // 清除自动刷新
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
        
        // 销毁图表
        this.charts.forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts.clear();
    }
    
    // 渲染错误页面
    renderError(error) {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;
        
        adminContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>仪表板加载失败</h2>
                    <p>${error.message || '无法加载仪表板数据'}</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
            </div>
        `;
    }
}

// 创建仪表板实例
const adminDashboard = new AdminDashboard();

// 导出渲染函数
export const render = (params, query) => adminDashboard.render(params, query);

// 导出默认实例
export default adminDashboard;
