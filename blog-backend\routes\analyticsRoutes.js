const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');

// Import analytics controller functions
const {
  getStats,
  getCategoryDistribution,
  getTagCloud,
  getPostsOverTime,
  getPopularPosts,
  getUserAnalytics,
  getContentAnalysis,
  getInteractionNetwork
} = require('../controllers/analyticsController');

// Basic analytics routes
router.get('/stats', protect, authorize('admin'), getStats);
router.get('/popular', getPopularPosts);
router.get('/tags', getTagCloud);

// Advanced MongoDB aggregation showcase routes
router.get('/category-distribution', getCategoryDistribution);
router.get('/posts-over-time', getPostsOverTime);
router.get('/user-analytics', getUserAnalytics);
router.get('/content-analysis', getContentAnalysis);
router.get('/interaction-network', getInteractionNetwork);

module.exports = router;