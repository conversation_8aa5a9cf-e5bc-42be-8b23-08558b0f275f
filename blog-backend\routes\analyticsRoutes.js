const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');

// Placeholder controller functions - in real implementation these would be imported
// from actual controller files
const getStats = (req, res) => {
  res.status(200).json({
    articleCount: 10,
    commentCount: 25,
    userCount: 5,
    categoryCount: 3
  });
};

const getPopularPosts = (req, res) => {
  res.status(200).json([
    { _id: '1', title: 'Popular Post 1', viewCount: 120 },
    { _id: '2', title: 'Popular Post 2', viewCount: 95 }
  ]);
};

const getTagCloud = (req, res) => {
  res.status(200).json([
    { name: 'javascript', count: 12 },
    { name: 'react', count: 8 },
    { name: 'test', count: 4 }
  ]);
};

// Define routes
router.get('/stats', protect, authorize('admin'), getStats);
router.get('/popular', getPopularPosts);
router.get('/tags', getTagCloud);

module.exports = router; 