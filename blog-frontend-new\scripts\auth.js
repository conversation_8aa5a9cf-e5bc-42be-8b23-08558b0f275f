import { api } from './api.js';
import { appState } from './state.js';
import { router } from './router.js';
import { CONFIG, SUCCESS_MESSAGES, ERROR_MESSAGES } from './config.js';
import { Utils } from './utils.js';

// 认证管理类
export class AuthManager {
    constructor() {
        this.initEventListeners();
    }
    
    // 初始化事件监听器
    initEventListeners() {
        // 登录表单
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // 登录/注册模态框
        document.addEventListener('click', this.handleAuthActions.bind(this));
        
        // 监听状态变化
        appState.subscribe(this.handleStateChange.bind(this));
    }
    
    // 处理表单提交
    async handleFormSubmit(event) {
        const form = event.target;
        
        if (form.id === 'loginForm') {
            event.preventDefault();
            await this.handleLogin(form);
        } else if (form.id === 'registerForm') {
            event.preventDefault();
            await this.handleRegister(form);
        } else if (form.id === 'adminLoginForm') {
            event.preventDefault();
            await this.handleAdminLogin(form);
        }
    }
    
    // 处理认证相关点击事件
    handleAuthActions(event) {
        const target = event.target;
        
        // 登录按钮
        if (target.id === 'loginBtn' || target.closest('#loginBtn')) {
            this.showLoginModal();
        }
        
        // 注册链接
        else if (target.id === 'showRegister') {
            this.showRegisterModal();
        }
        
        // 登录链接
        else if (target.id === 'showLogin') {
            this.showLoginModal();
        }
        
        // 登出按钮
        else if (target.id === 'logoutBtn' || target.id === 'adminLogout' || target.id === 'headerLogout') {
            this.logout();
        }
        
        // 模态框关闭
        else if (target.classList.contains('modal-close') || target.classList.contains('modal-overlay')) {
            this.closeAuthModals();
        }
    }
    
    // 处理登录
    async handleLogin(form) {
        try {
            const formData = new FormData(form);
            const credentials = {
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            // 验证输入
            if (!this.validateLoginInput(credentials)) {
                return;
            }
            
            // 显示加载状态
            appState.setLoading(true);
            
            // 调用登录API
            const response = await api.login(credentials);
            
            // 更新用户状态
            appState.setUser(response.user);
            
            // 显示成功消息
            appState.addToast({
                type: 'success',
                message: SUCCESS_MESSAGES.LOGIN
            });
            
            // 关闭模态框
            this.closeAuthModals();
            
            // 重置表单
            form.reset();
            
            // 更新UI
            this.updateAuthUI();
            
        } catch (error) {
            console.error('Login error:', error);
            appState.addToast({
                type: 'error',
                message: error.message || ERROR_MESSAGES.UNKNOWN
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理注册
    async handleRegister(form) {
        try {
            const formData = new FormData(form);
            const userData = {
                name: formData.get('name'),
                email: formData.get('email'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword')
            };
            
            // 验证输入
            if (!this.validateRegisterInput(userData)) {
                return;
            }
            
            // 显示加载状态
            appState.setLoading(true);
            
            // 调用注册API
            const response = await api.register(userData);
            
            // 显示成功消息
            appState.addToast({
                type: 'success',
                message: SUCCESS_MESSAGES.REGISTER
            });
            
            // 自动登录
            if (response.token) {
                appState.setUser(response.user);
                this.updateAuthUI();
            }
            
            // 关闭模态框
            this.closeAuthModals();
            
            // 重置表单
            form.reset();
            
        } catch (error) {
            console.error('Register error:', error);
            appState.addToast({
                type: 'error',
                message: error.message || ERROR_MESSAGES.UNKNOWN
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理管理员登录
    async handleAdminLogin(form) {
        try {
            const formData = new FormData(form);
            const credentials = {
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            // 验证输入
            if (!this.validateLoginInput(credentials)) {
                return;
            }
            
            // 显示加载状态
            appState.setLoading(true);
            
            // 调用登录API
            const response = await api.login(credentials);
            
            // 检查管理员权限
            if (response.user.role !== 'admin') {
                appState.addToast({
                    type: 'error',
                    message: '权限不足，需要管理员权限'
                });
                return;
            }
            
            // 更新用户状态
            appState.setUser(response.user);
            
            // 显示成功消息
            appState.addToast({
                type: 'success',
                message: SUCCESS_MESSAGES.LOGIN
            });
            
            // 隐藏登录模态框
            const loginModal = document.getElementById('adminLoginModal');
            if (loginModal) {
                loginModal.classList.add('hidden');
            }
            
            // 重置表单
            form.reset();
            
            // 导航到仪表板
            router.navigate('/admin');
            
        } catch (error) {
            console.error('Admin login error:', error);
            appState.addToast({
                type: 'error',
                message: error.message || ERROR_MESSAGES.UNKNOWN
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 登出
    logout() {
        try {
            // 清除用户状态
            appState.clearUser();
            
            // 调用API登出
            api.logout();
            
            // 显示成功消息
            appState.addToast({
                type: 'success',
                message: SUCCESS_MESSAGES.LOGOUT
            });
            
            // 更新UI
            this.updateAuthUI();
            
            // 如果在管理后台，跳转到首页
            if (window.location.pathname.startsWith('/admin')) {
                router.navigate('/');
            }
            
        } catch (error) {
            console.error('Logout error:', error);
        }
    }
    
    // 验证登录输入
    validateLoginInput(credentials) {
        if (!credentials.email) {
            appState.addToast({
                type: 'error',
                message: '请输入邮箱地址'
            });
            return false;
        }
        
        if (!Utils.validateEmail(credentials.email)) {
            appState.addToast({
                type: 'error',
                message: '请输入有效的邮箱地址'
            });
            return false;
        }
        
        if (!credentials.password) {
            appState.addToast({
                type: 'error',
                message: '请输入密码'
            });
            return false;
        }
        
        return true;
    }
    
    // 验证注册输入
    validateRegisterInput(userData) {
        if (!userData.name) {
            appState.addToast({
                type: 'error',
                message: '请输入用户名'
            });
            return false;
        }
        
        if (!Utils.validateUsername(userData.name)) {
            appState.addToast({
                type: 'error',
                message: '用户名只能包含字母、数字和下划线，长度3-20位'
            });
            return false;
        }
        
        if (!userData.email) {
            appState.addToast({
                type: 'error',
                message: '请输入邮箱地址'
            });
            return false;
        }
        
        if (!Utils.validateEmail(userData.email)) {
            appState.addToast({
                type: 'error',
                message: '请输入有效的邮箱地址'
            });
            return false;
        }
        
        if (!userData.password) {
            appState.addToast({
                type: 'error',
                message: '请输入密码'
            });
            return false;
        }
        
        if (userData.password.length < 6) {
            appState.addToast({
                type: 'error',
                message: '密码长度至少6位'
            });
            return false;
        }
        
        if (userData.password !== userData.confirmPassword) {
            appState.addToast({
                type: 'error',
                message: '两次输入的密码不一致'
            });
            return false;
        }
        
        return true;
    }
    
    // 显示登录模态框
    showLoginModal() {
        this.closeAuthModals();
        const loginModal = document.getElementById('loginModal');
        if (loginModal) {
            loginModal.classList.remove('hidden');
        }
    }
    
    // 显示注册模态框
    showRegisterModal() {
        this.closeAuthModals();
        const registerModal = document.getElementById('registerModal');
        if (registerModal) {
            registerModal.classList.remove('hidden');
        }
    }
    
    // 关闭认证模态框
    closeAuthModals() {
        const modals = ['loginModal', 'registerModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
            }
        });
    }
    
    // 更新认证UI
    updateAuthUI() {
        const state = appState.getState();
        const authButtons = document.getElementById('authButtons');
        const userMenu = document.getElementById('userMenu');
        
        if (state.isAuthenticated && state.user) {
            // 已登录状态
            if (authButtons) authButtons.classList.add('hidden');
            if (userMenu) {
                userMenu.classList.remove('hidden');
                const userName = userMenu.querySelector('.user-name');
                if (userName) {
                    userName.textContent = state.user.name;
                }
            }
        } else {
            // 未登录状态
            if (authButtons) authButtons.classList.remove('hidden');
            if (userMenu) userMenu.classList.add('hidden');
        }
        
        // 更新管理后台用户信息
        const adminUserName = document.getElementById('adminUserName');
        if (adminUserName && state.user) {
            adminUserName.textContent = state.user.name;
        }
    }
    
    // 处理状态变化
    handleStateChange(currentState, prevState) {
        // 认证状态变化时更新UI
        if (currentState.isAuthenticated !== prevState.isAuthenticated) {
            this.updateAuthUI();
        }
    }
    
    // 检查认证状态
    async checkAuthStatus() {
        try {
            const token = Utils.storage.get(CONFIG.STORAGE_KEYS.TOKEN);
            if (!token) return false;
            
            // 验证token有效性
            const user = await api.getMe();
            appState.setUser(user);
            
            return true;
        } catch (error) {
            // Token无效，清除本地存储
            appState.clearUser();
            return false;
        }
    }
    
    // 初始化认证状态
    async init() {
        await this.checkAuthStatus();
        this.updateAuthUI();
        
        // 检查管理后台访问权限
        if (window.location.pathname.startsWith('/admin') && window.location.pathname !== '/admin/login') {
            const state = appState.getState();
            if (!state.isAuthenticated || !state.isAdmin) {
                // 显示管理员登录模态框
                const adminLoginModal = document.getElementById('adminLoginModal');
                if (adminLoginModal) {
                    adminLoginModal.classList.remove('hidden');
                }
            }
        }
    }
}

// 创建全局认证管理实例
export const authManager = new AuthManager();

// 导出默认认证管理实例
export default authManager;
