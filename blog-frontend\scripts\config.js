// ========== 应用配置 ==========
const CONFIG = {
    // API配置
    API: {
        BASE_URL: 'http://localhost:5000/api',
        TIMEOUT: 10000,
        RETRY_ATTEMPTS: 3
    },
    
    // 分页配置
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 10,
        MAX_PAGE_SIZE: 50
    },
    
    // 搜索配置
    SEARCH: {
        MIN_QUERY_LENGTH: 2,
        DEBOUNCE_DELAY: 300,
        MAX_SUGGESTIONS: 5
    },
    
    // 缓存配置
    CACHE: {
        ENABLED: true,
        TTL: 5 * 60 * 1000, // 5分钟
        MAX_SIZE: 100
    },
    
    // UI配置
    UI: {
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 3000,
        LOADING_DELAY: 200
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        AUTH_TOKEN: 'blog_auth_token',
        USER_INFO: 'blog_user_info',
        THEME: 'blog_theme',
        LANGUAGE: 'blog_language'
    },
    
    // 默认值
    DEFAULTS: {
        THEME: 'auto', // auto, light, dark
        LANGUAGE: 'zh-CN',
        ARTICLE_EXCERPT_LENGTH: 150,
        FEATURED_ARTICLES_COUNT: 6
    },
    
    // 错误消息
    ERROR_MESSAGES: {
        NETWORK_ERROR: '网络连接失败，请检查网络设置',
        SERVER_ERROR: '服务器错误，请稍后重试',
        UNAUTHORIZED: '请先登录',
        FORBIDDEN: '权限不足',
        NOT_FOUND: '请求的资源不存在',
        VALIDATION_ERROR: '输入数据格式错误',
        UNKNOWN_ERROR: '未知错误，请联系管理员'
    },
    
    // 成功消息
    SUCCESS_MESSAGES: {
        LOGIN_SUCCESS: '登录成功',
        LOGOUT_SUCCESS: '退出成功',
        REGISTER_SUCCESS: '注册成功',
        COMMENT_SUCCESS: '评论发布成功',
        UPDATE_SUCCESS: '更新成功',
        DELETE_SUCCESS: '删除成功'
    },
    
    // 文章分类图标映射
    CATEGORY_ICONS: {
        '技术': 'fas fa-code',
        '生活': 'fas fa-heart',
        '旅行': 'fas fa-plane',
        '美食': 'fas fa-utensils',
        '读书': 'fas fa-book',
        '电影': 'fas fa-film',
        '音乐': 'fas fa-music',
        '摄影': 'fas fa-camera',
        '运动': 'fas fa-running',
        '游戏': 'fas fa-gamepad',
        '默认': 'fas fa-folder'
    },
    
    // 日期格式化选项
    DATE_FORMAT: {
        FULL: {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        },
        SHORT: {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        },
        RELATIVE: true // 使用相对时间格式
    },
    
    // 媒体查询断点
    BREAKPOINTS: {
        MOBILE: 480,
        TABLET: 768,
        DESKTOP: 1024,
        LARGE: 1200
    }
};

// ========== 工具函数 ==========
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 格式化日期
    formatDate(date, format = 'FULL') {
        const d = new Date(date);
        const now = new Date();
        const diff = now - d;
        
        // 相对时间格式
        if (CONFIG.DATE_FORMAT.RELATIVE && format === 'RELATIVE') {
            const minutes = Math.floor(diff / 60000);
            const hours = Math.floor(diff / 3600000);
            const days = Math.floor(diff / 86400000);
            
            if (minutes < 1) return '刚刚';
            if (minutes < 60) return `${minutes}分钟前`;
            if (hours < 24) return `${hours}小时前`;
            if (days < 7) return `${days}天前`;
        }
        
        // 标准格式
        const options = CONFIG.DATE_FORMAT[format] || CONFIG.DATE_FORMAT.FULL;
        return d.toLocaleDateString('zh-CN', options);
    },
    
    // 截取文本
    truncateText(text, length = CONFIG.DEFAULTS.ARTICLE_EXCERPT_LENGTH) {
        if (!text || text.length <= length) return text;
        return text.substring(0, length).trim() + '...';
    },
    
    // 生成随机ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    // 获取分类图标
    getCategoryIcon(categoryName) {
        return CONFIG.CATEGORY_ICONS[categoryName] || CONFIG.CATEGORY_ICONS['默认'];
    },
    
    // 格式化数字
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },
    
    // 检查是否为移动设备
    isMobile() {
        return window.innerWidth <= CONFIG.BREAKPOINTS.MOBILE;
    },
    
    // 检查是否为平板设备
    isTablet() {
        return window.innerWidth <= CONFIG.BREAKPOINTS.TABLET && window.innerWidth > CONFIG.BREAKPOINTS.MOBILE;
    },
    
    // 平滑滚动到元素
    scrollToElement(element, offset = 80) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    },
    
    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
        }
    },
    
    // 验证邮箱格式
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // 验证密码强度
    validatePassword(password) {
        const minLength = 6;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        
        return {
            isValid: password.length >= minLength,
            length: password.length >= minLength,
            hasUpperCase,
            hasLowerCase,
            hasNumbers,
            score: [password.length >= minLength, hasUpperCase, hasLowerCase, hasNumbers].filter(Boolean).length
        };
    },
    
    // 安全的JSON解析
    safeJsonParse(str, defaultValue = null) {
        try {
            return JSON.parse(str);
        } catch (e) {
            return defaultValue;
        }
    },
    
    // 获取URL参数
    getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        const result = {};
        for (const [key, value] of params) {
            result[key] = value;
        }
        return result;
    },
    
    // 设置URL参数
    setUrlParams(params) {
        const url = new URL(window.location);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.history.replaceState({}, '', url);
    }
};

// ========== 事件系统 ==========
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    off(event, callback) {
        if (!this.events[event]) return;
        this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
    
    emit(event, data) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => callback(data));
    }
}

// 全局事件总线
window.EventBus = new EventEmitter();

// ========== 导出配置 ==========
window.CONFIG = CONFIG;
window.Utils = Utils;
