const Article = require('../models/Article');
const Category = require('../models/Category');
const Comment = require('../models/Comment');
const User = require('../models/User');

// @desc    Get basic statistics
// @route   GET /api/analytics/stats
// @access  Public
exports.getStats = async (req, res, next) => {
  try {
    const totalArticles = await Article.countDocuments({ isPublished: true });
    const totalCategories = await Category.countDocuments();
    const totalComments = await Comment.countDocuments();
    const totalDrafts = await Article.countDocuments({ isPublished: false });
    
    res.status(200).json({
      success: true,
      data: {
        totalArticles,
        totalCategories,
        totalComments,
        totalDrafts
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get category distribution
// @route   GET /api/analytics/category-distribution
// @access  Public
exports.getCategoryDistribution = async (req, res, next) => {
  try {
    const distribution = await Article.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'categories',
          localField: '_id',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      },
      {
        $unwind: '$categoryInfo'
      },
      {
        $project: {
          _id: 1,
          count: 1,
          name: '$categoryInfo.name'
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      count: distribution.length,
      data: distribution
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get tag cloud data
// @route   GET /api/analytics/tag-cloud
// @access  Public
exports.getTagCloud = async (req, res, next) => {
  try {
    const tagCloud = await Article.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $unwind: '$tags'
      },
      {
        $group: {
          _id: '$tags',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          tag: '$_id',
          count: 1
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      count: tagCloud.length,
      data: tagCloud
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get posts over time
// @route   GET /api/analytics/posts-over-time
// @access  Public
exports.getPostsOverTime = async (req, res, next) => {
  try {
    const timeUnit = req.query.timeUnit || 'month'; // 'day', 'month', 'year'
    
    let groupFormat;
    
    switch (timeUnit) {
      case 'day':
        groupFormat = { $dateToString: { format: '%Y-%m-%d', date: '$publishDate' } };
        break;
      case 'month':
        groupFormat = { $dateToString: { format: '%Y-%m', date: '$publishDate' } };
        break;
      case 'year':
        groupFormat = { $dateToString: { format: '%Y', date: '$publishDate' } };
        break;
      default:
        groupFormat = { $dateToString: { format: '%Y-%m', date: '$publishDate' } };
    }
    
    const postsOverTime = await Article.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $group: {
          _id: groupFormat,
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      count: postsOverTime.length,
      data: postsOverTime
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get popular posts
// @route   GET /api/analytics/popular-posts
// @access  Public
exports.getPopularPosts = async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 5;
    const sortField = req.query.sortBy === 'comments' ? 'commentCount' : 'viewCount';
    
    let popularPosts;
    
    if (sortField === 'commentCount') {
      // Aggregate to count comments and sort by comment count
      popularPosts = await Article.aggregate([
        {
          $match: { isPublished: true }
        },
        {
          $lookup: {
            from: 'comments',
            localField: '_id',
            foreignField: 'articleId',
            as: 'comments'
          }
        },
        {
          $addFields: {
            commentCount: { $size: '$comments' }
          }
        },
        {
          $project: {
            _id: 1,
            title: 1,
            viewCount: 1,
            publishDate: 1,
            commentCount: 1
          }
        },
        {
          $sort: { commentCount: -1 }
        },
        {
          $limit: limit
        }
      ]);
    } else {
      // Just sort by viewCount
      popularPosts = await Article.find({ isPublished: true })
        .select('title viewCount publishDate')
        .sort('-viewCount')
        .limit(limit);
    }
    
    res.status(200).json({
      success: true,
      count: popularPosts.length,
      data: popularPosts
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get advanced user analytics (展示MongoDB聚合管道的强大功能)
// @route   GET /api/analytics/user-analytics
// @access  Public
exports.getUserAnalytics = async (req, res, next) => {
  try {
    const userAnalytics = await User.aggregate([
      {
        // 第一阶段：查找所有用户
        $match: {}
      },
      {
        // 第二阶段：关联用户的文章
        $lookup: {
          from: 'articles',
          localField: '_id',
          foreignField: 'author',
          as: 'articles'
        }
      },
      {
        // 第三阶段：关联用户的评论
        $lookup: {
          from: 'comments',
          localField: '_id',
          foreignField: 'author',
          as: 'comments'
        }
      },
      {
        // 第四阶段：计算用户统计数据
        $addFields: {
          articleCount: { $size: '$articles' },
          commentCount: { $size: '$comments' },
          publishedArticleCount: {
            $size: {
              $filter: {
                input: '$articles',
                cond: { $eq: ['$$this.isPublished', true] }
              }
            }
          },
          totalViews: {
            $sum: '$articles.viewCount'
          },
          avgViewsPerArticle: {
            $cond: {
              if: { $gt: [{ $size: '$articles' }, 0] },
              then: { $divide: [{ $sum: '$articles.viewCount' }, { $size: '$articles' }] },
              else: 0
            }
          }
        }
      },
      {
        // 第五阶段：计算用户活跃度评分
        $addFields: {
          activityScore: {
            $add: [
              { $multiply: ['$publishedArticleCount', 10] },
              { $multiply: ['$commentCount', 2] },
              { $divide: ['$totalViews', 100] }
            ]
          }
        }
      },
      {
        // 第六阶段：选择需要的字段
        $project: {
          _id: 1,
          username: 1,
          email: 1,
          role: 1,
          createdAt: 1,
          articleCount: 1,
          publishedArticleCount: 1,
          commentCount: 1,
          totalViews: 1,
          avgViewsPerArticle: { $round: ['$avgViewsPerArticle', 2] },
          activityScore: { $round: ['$activityScore', 2] }
        }
      },
      {
        // 第七阶段：按活跃度排序
        $sort: { activityScore: -1 }
      }
    ]);

    res.status(200).json({
      success: true,
      count: userAnalytics.length,
      data: userAnalytics,
      description: '用户活跃度分析 - 展示MongoDB聚合管道的强大数据处理能力'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content analysis (展示MongoDB复杂文档查询和数组操作)
// @route   GET /api/analytics/content-analysis
// @access  Public
exports.getContentAnalysis = async (req, res, next) => {
  try {
    const contentAnalysis = await Article.aggregate([
      {
        // 匹配已发布的文章
        $match: { isPublished: true }
      },
      {
        // 添加计算字段
        $addFields: {
          contentLength: { $strLenCP: '$content' },
          titleLength: { $strLenCP: '$title' },
          tagCount: { $size: { $ifNull: ['$tags', []] } },
          wordCount: {
            $size: {
              $split: [
                { $trim: { input: '$content' } },
                ' '
              ]
            }
          }
        }
      },
      {
        // 分组统计
        $group: {
          _id: null,
          totalArticles: { $sum: 1 },
          avgContentLength: { $avg: '$contentLength' },
          avgTitleLength: { $avg: '$titleLength' },
          avgWordCount: { $avg: '$wordCount' },
          avgTagCount: { $avg: '$tagCount' },
          maxContentLength: { $max: '$contentLength' },
          minContentLength: { $min: '$contentLength' },
          totalViews: { $sum: '$viewCount' },
          avgViews: { $avg: '$viewCount' }
        }
      },
      {
        // 格式化输出
        $project: {
          _id: 0,
          totalArticles: 1,
          avgContentLength: { $round: ['$avgContentLength', 0] },
          avgTitleLength: { $round: ['$avgTitleLength', 0] },
          avgWordCount: { $round: ['$avgWordCount', 0] },
          avgTagCount: { $round: ['$avgTagCount', 1] },
          maxContentLength: 1,
          minContentLength: 1,
          totalViews: 1,
          avgViews: { $round: ['$avgViews', 0] }
        }
      }
    ]);

    // 获取标签使用情况分析
    const tagAnalysis = await Article.aggregate([
      { $match: { isPublished: true } },
      { $unwind: { path: '$tags', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: '$tags',
          count: { $sum: 1 },
          articles: { $push: { title: '$title', _id: '$_id' } }
        }
      },
      {
        $project: {
          tag: '$_id',
          count: 1,
          articles: { $slice: ['$articles', 3] }, // 只显示前3篇文章
          _id: 0
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.status(200).json({
      success: true,
      data: {
        contentStats: contentAnalysis[0] || {},
        tagAnalysis: tagAnalysis
      },
      description: '内容分析 - 展示MongoDB文档字段计算、数组操作和复杂聚合'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get interaction network analysis (展示MongoDB多集合关联和复杂数据关系)
// @route   GET /api/analytics/interaction-network
// @access  Public
exports.getInteractionNetwork = async (req, res, next) => {
  try {
    // 分析用户之间的互动关系
    const interactionNetwork = await Comment.aggregate([
      {
        // 关联评论的文章信息
        $lookup: {
          from: 'articles',
          localField: 'article',
          foreignField: '_id',
          as: 'articleInfo'
        }
      },
      {
        $unwind: '$articleInfo'
      },
      {
        // 关联评论者信息
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'commenterInfo'
        }
      },
      {
        $unwind: '$commenterInfo'
      },
      {
        // 关联文章作者信息
        $lookup: {
          from: 'users',
          localField: 'articleInfo.author',
          foreignField: '_id',
          as: 'authorInfo'
        }
      },
      {
        $unwind: '$authorInfo'
      },
      {
        // 过滤掉自己评论自己文章的情况
        $match: {
          $expr: { $ne: ['$commenterInfo._id', '$authorInfo._id'] }
        }
      },
      {
        // 分组统计互动关系
        $group: {
          _id: {
            commenter: '$commenterInfo._id',
            commenterName: '$commenterInfo.username',
            author: '$authorInfo._id',
            authorName: '$authorInfo.username'
          },
          interactionCount: { $sum: 1 },
          articles: {
            $addToSet: {
              title: '$articleInfo.title',
              id: '$articleInfo._id'
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          commenter: {
            id: '$_id.commenter',
            name: '$_id.commenterName'
          },
          author: {
            id: '$_id.author',
            name: '$_id.authorName'
          },
          interactionCount: 1,
          articlesInteracted: { $size: '$articles' },
          articles: { $slice: ['$articles', 3] }
        }
      },
      {
        $sort: { interactionCount: -1 }
      },
      {
        $limit: 20
      }
    ]);

    // 获取最活跃的互动用户
    const topInteractors = await Comment.aggregate([
      {
        $group: {
          _id: '$author',
          commentCount: { $sum: 1 },
          articlesCommented: { $addToSet: '$article' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $unwind: '$userInfo'
      },
      {
        $project: {
          _id: 0,
          user: {
            id: '$userInfo._id',
            name: '$userInfo.username',
            role: '$userInfo.role'
          },
          commentCount: 1,
          articlesCommented: { $size: '$articlesCommented' }
        }
      },
      {
        $sort: { commentCount: -1 }
      },
      {
        $limit: 10
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        interactionNetwork,
        topInteractors
      },
      description: '用户互动网络分析 - 展示MongoDB多集合关联查询和复杂数据关系分析'
    });
  } catch (err) {
    next(err);
  }
};