const Article = require('../models/Article');
const Category = require('../models/Category');
const Comment = require('../models/Comment');

// @desc    Get basic statistics
// @route   GET /api/analytics/stats
// @access  Public
exports.getStats = async (req, res, next) => {
  try {
    const totalArticles = await Article.countDocuments({ isPublished: true });
    const totalCategories = await Category.countDocuments();
    const totalComments = await Comment.countDocuments();
    const totalDrafts = await Article.countDocuments({ isPublished: false });
    
    res.status(200).json({
      success: true,
      data: {
        totalArticles,
        totalCategories,
        totalComments,
        totalDrafts
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get category distribution
// @route   GET /api/analytics/category-distribution
// @access  Public
exports.getCategoryDistribution = async (req, res, next) => {
  try {
    const distribution = await Article.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'categories',
          localField: '_id',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      },
      {
        $unwind: '$categoryInfo'
      },
      {
        $project: {
          _id: 1,
          count: 1,
          name: '$categoryInfo.name'
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      count: distribution.length,
      data: distribution
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get tag cloud data
// @route   GET /api/analytics/tag-cloud
// @access  Public
exports.getTagCloud = async (req, res, next) => {
  try {
    const tagCloud = await Article.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $unwind: '$tags'
      },
      {
        $group: {
          _id: '$tags',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          tag: '$_id',
          count: 1
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      count: tagCloud.length,
      data: tagCloud
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get posts over time
// @route   GET /api/analytics/posts-over-time
// @access  Public
exports.getPostsOverTime = async (req, res, next) => {
  try {
    const timeUnit = req.query.timeUnit || 'month'; // 'day', 'month', 'year'
    
    let groupFormat;
    
    switch (timeUnit) {
      case 'day':
        groupFormat = { $dateToString: { format: '%Y-%m-%d', date: '$publishDate' } };
        break;
      case 'month':
        groupFormat = { $dateToString: { format: '%Y-%m', date: '$publishDate' } };
        break;
      case 'year':
        groupFormat = { $dateToString: { format: '%Y', date: '$publishDate' } };
        break;
      default:
        groupFormat = { $dateToString: { format: '%Y-%m', date: '$publishDate' } };
    }
    
    const postsOverTime = await Article.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $group: {
          _id: groupFormat,
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      count: postsOverTime.length,
      data: postsOverTime
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get popular posts
// @route   GET /api/analytics/popular-posts
// @access  Public
exports.getPopularPosts = async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 5;
    const sortField = req.query.sortBy === 'comments' ? 'commentCount' : 'viewCount';
    
    let popularPosts;
    
    if (sortField === 'commentCount') {
      // Aggregate to count comments and sort by comment count
      popularPosts = await Article.aggregate([
        {
          $match: { isPublished: true }
        },
        {
          $lookup: {
            from: 'comments',
            localField: '_id',
            foreignField: 'articleId',
            as: 'comments'
          }
        },
        {
          $addFields: {
            commentCount: { $size: '$comments' }
          }
        },
        {
          $project: {
            _id: 1,
            title: 1,
            viewCount: 1,
            publishDate: 1,
            commentCount: 1
          }
        },
        {
          $sort: { commentCount: -1 }
        },
        {
          $limit: limit
        }
      ]);
    } else {
      // Just sort by viewCount
      popularPosts = await Article.find({ isPublished: true })
        .select('title viewCount publishDate')
        .sort('-viewCount')
        .limit(limit);
    }
    
    res.status(200).json({
      success: true,
      count: popularPosts.length,
      data: popularPosts
    });
  } catch (err) {
    next(err);
  }
}; 