<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 现代博客系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body class="admin-body">
    <!-- 管理后台侧边栏 -->
    <aside class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-cog"></i>
                <span>管理后台</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="/admin" class="nav-link active" data-route="/admin">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/articles" class="nav-link" data-route="/admin/articles">
                        <i class="fas fa-file-alt"></i>
                        <span>文章管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/articles/new" class="nav-link" data-route="/admin/articles/new">
                        <i class="fas fa-plus"></i>
                        <span>新建文章</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/categories" class="nav-link" data-route="/admin/categories">
                        <i class="fas fa-folder"></i>
                        <span>分类管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/comments" class="nav-link" data-route="/admin/comments">
                        <i class="fas fa-comments"></i>
                        <span>评论管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/users" class="nav-link" data-route="/admin/users">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/settings" class="nav-link" data-route="/admin/settings">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="adminUserName">管理员</span>
                    <span class="user-role">Administrator</span>
                </div>
            </div>
            <div class="sidebar-actions">
                <a href="/" class="action-btn" title="返回前台">
                    <i class="fas fa-home"></i>
                </a>
                <button class="action-btn" id="adminLogout" title="退出登录">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- 管理后台主内容区 -->
    <main class="admin-main">
        <!-- 顶部导航栏 -->
        <header class="admin-header">
            <div class="header-left">
                <button class="mobile-sidebar-toggle" id="mobileSidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">仪表板</h1>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <button class="header-btn" id="refreshBtn" title="刷新数据">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="header-btn" id="notificationBtn" title="通知">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                
                <div class="admin-user-menu">
                    <div class="user-avatar" id="headerUserAvatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-dropdown">
                        <a href="/admin/profile" class="dropdown-item">个人资料</a>
                        <a href="/admin/settings" class="dropdown-item">系统设置</a>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item" id="headerLogout">退出登录</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="admin-content" id="adminContent">
            <!-- 页面内容将通过路由动态加载 -->
        </div>
    </main>

    <!-- 加载指示器 -->
    <div class="loading-overlay hidden" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- 确认对话框 -->
    <div class="modal hidden" id="confirmModal">
        <div class="modal-overlay"></div>
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3 id="confirmTitle">确认操作</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">您确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="confirmCancel">取消</button>
                <button class="btn btn-danger" id="confirmOk">确认</button>
            </div>
        </div>
    </div>

    <!-- 登录模态框（管理员登录） -->
    <div class="modal" id="adminLoginModal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>管理员登录</h2>
            </div>
            <div class="modal-body">
                <form id="adminLoginForm" class="auth-form">
                    <div class="form-group">
                        <label for="adminEmail">管理员邮箱</label>
                        <input type="email" id="adminEmail" name="email" required class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="adminPassword">密码</label>
                        <input type="password" id="adminPassword" name="password" required class="form-input">
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">登录</button>
                </form>
                <div class="auth-links">
                    <p><a href="/">返回前台</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script type="module" src="scripts/config.js"></script>
    <script type="module" src="scripts/utils.js"></script>
    <script type="module" src="scripts/api.js"></script>
    <script type="module" src="scripts/router.js"></script>
    <script type="module" src="scripts/auth.js"></script>
    <script type="module" src="scripts/state.js"></script>
    <script type="module" src="scripts/ui.js"></script>
    <script type="module" src="scripts/admin.js"></script>
</body>
</html>
