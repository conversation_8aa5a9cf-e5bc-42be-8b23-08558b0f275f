const Article = require('../models/Article');
const Category = require('../models/Category');
const Comment = require('../models/Comment');
const User = require('../models/User');

// @desc    Full text search across articles (展示MongoDB全文搜索功能)
// @route   GET /api/search/articles
// @access  Public
exports.searchArticles = async (req, res, next) => {
  try {
    const { q, category, tags, sortBy = 'relevance', limit = 10, page = 1 } = req.query;
    
    if (!q) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }

    // 构建搜索管道
    const searchPipeline = [];

    // 第一阶段：全文搜索
    searchPipeline.push({
      $match: {
        $and: [
          { isPublished: true },
          {
            $text: {
              $search: q,
              $caseSensitive: false,
              $diacriticSensitive: false
            }
          }
        ]
      }
    });

    // 添加分类筛选
    if (category) {
      searchPipeline.push({
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      });
      searchPipeline.push({
        $match: {
          'categoryInfo.name': { $regex: category, $options: 'i' }
        }
      });
    }

    // 添加标签筛选
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      searchPipeline.push({
        $match: {
          tags: { $in: tagArray }
        }
      });
    }

    // 添加相关性评分和其他计算字段
    searchPipeline.push({
      $addFields: {
        score: { $meta: 'textScore' },
        contentPreview: {
          $substr: ['$content', 0, 200]
        },
        matchedTags: {
          $filter: {
            input: '$tags',
            cond: {
              $regexMatch: {
                input: '$$this',
                regex: q,
                options: 'i'
              }
            }
          }
        }
      }
    });

    // 关联作者信息
    searchPipeline.push({
      $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'authorInfo'
      }
    });

    searchPipeline.push({
      $unwind: '$authorInfo'
    });

    // 选择返回字段
    searchPipeline.push({
      $project: {
        _id: 1,
        title: 1,
        contentPreview: 1,
        tags: 1,
        matchedTags: 1,
        viewCount: 1,
        publishDate: 1,
        score: 1,
        author: {
          id: '$authorInfo._id',
          username: '$authorInfo.username'
        },
        category: '$categoryInfo.name'
      }
    });

    // 排序
    const sortOptions = {
      relevance: { score: { $meta: 'textScore' } },
      date: { publishDate: -1 },
      views: { viewCount: -1 }
    };

    searchPipeline.push({
      $sort: sortOptions[sortBy] || sortOptions.relevance
    });

    // 分页
    const skip = (parseInt(page) - 1) * parseInt(limit);
    searchPipeline.push({ $skip: skip });
    searchPipeline.push({ $limit: parseInt(limit) });

    const results = await Article.aggregate(searchPipeline);

    // 获取总数（用于分页）
    const countPipeline = searchPipeline.slice(0, -2); // 移除skip和limit
    countPipeline.push({ $count: 'total' });
    const countResult = await Article.aggregate(countPipeline);
    const total = countResult[0]?.total || 0;

    res.status(200).json({
      success: true,
      count: results.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      data: results,
      searchQuery: q,
      description: 'MongoDB全文搜索 - 支持多字段搜索、相关性排序、分类和标签筛选'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Advanced search with multiple criteria (展示MongoDB复杂查询能力)
// @route   GET /api/search/advanced
// @access  Public
exports.advancedSearch = async (req, res, next) => {
  try {
    const {
      keyword,
      author,
      category,
      tags,
      dateFrom,
      dateTo,
      minViews,
      maxViews,
      sortBy = 'date',
      limit = 10,
      page = 1
    } = req.query;

    // 构建复杂查询条件
    const matchConditions = { isPublished: true };

    // 关键词搜索（支持标题和内容）
    if (keyword) {
      matchConditions.$or = [
        { title: { $regex: keyword, $options: 'i' } },
        { content: { $regex: keyword, $options: 'i' } },
        { tags: { $in: [new RegExp(keyword, 'i')] } }
      ];
    }

    // 日期范围
    if (dateFrom || dateTo) {
      matchConditions.publishDate = {};
      if (dateFrom) {
        matchConditions.publishDate.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        matchConditions.publishDate.$lte = new Date(dateTo);
      }
    }

    // 浏览量范围
    if (minViews || maxViews) {
      matchConditions.viewCount = {};
      if (minViews) {
        matchConditions.viewCount.$gte = parseInt(minViews);
      }
      if (maxViews) {
        matchConditions.viewCount.$lte = parseInt(maxViews);
      }
    }

    // 标签筛选
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      matchConditions.tags = { $in: tagArray };
    }

    const searchPipeline = [
      { $match: matchConditions }
    ];

    // 作者筛选
    if (author) {
      searchPipeline.push({
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorInfo'
        }
      });
      searchPipeline.push({
        $match: {
          'authorInfo.username': { $regex: author, $options: 'i' }
        }
      });
    } else {
      searchPipeline.push({
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorInfo'
        }
      });
    }

    // 分类筛选
    if (category) {
      searchPipeline.push({
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      });
      searchPipeline.push({
        $match: {
          'categoryInfo.name': { $regex: category, $options: 'i' }
        }
      });
    } else {
      searchPipeline.push({
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      });
    }

    // 添加评论数统计
    searchPipeline.push({
      $lookup: {
        from: 'comments',
        localField: '_id',
        foreignField: 'article',
        as: 'comments'
      }
    });

    // 格式化输出
    searchPipeline.push({
      $project: {
        _id: 1,
        title: 1,
        content: { $substr: ['$content', 0, 300] },
        tags: 1,
        viewCount: 1,
        publishDate: 1,
        author: {
          id: { $arrayElemAt: ['$authorInfo._id', 0] },
          username: { $arrayElemAt: ['$authorInfo.username', 0] }
        },
        category: {
          id: { $arrayElemAt: ['$categoryInfo._id', 0] },
          name: { $arrayElemAt: ['$categoryInfo.name', 0] }
        },
        commentCount: { $size: '$comments' }
      }
    });

    // 排序
    const sortOptions = {
      date: { publishDate: -1 },
      views: { viewCount: -1 },
      comments: { commentCount: -1 },
      title: { title: 1 }
    };

    searchPipeline.push({
      $sort: sortOptions[sortBy] || sortOptions.date
    });

    // 分页
    const skip = (parseInt(page) - 1) * parseInt(limit);
    searchPipeline.push({ $skip: skip });
    searchPipeline.push({ $limit: parseInt(limit) });

    const results = await Article.aggregate(searchPipeline);

    res.status(200).json({
      success: true,
      count: results.length,
      data: results,
      searchCriteria: {
        keyword,
        author,
        category,
        tags,
        dateFrom,
        dateTo,
        minViews,
        maxViews
      },
      description: 'MongoDB高级搜索 - 展示复杂查询条件组合、多集合关联和灵活的数据筛选'
    });
  } catch (err) {
    next(err);
  }
};
