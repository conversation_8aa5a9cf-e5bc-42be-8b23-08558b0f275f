import { api } from '../../api.js';
import { appState } from '../../state.js';
import { router } from '../../router.js';
import { CONFIG } from '../../config.js';
import { Utils } from '../../utils.js';

// 管理后台分类管理组件
export class AdminCategories {
    constructor() {
        this.categories = [];
        this.editingCategory = null;
        this.isModalOpen = false;
    }
    
    // 渲染分类管理页面
    async render(params = {}, query = {}) {
        try {
            // 显示加载状态
            appState.setLoading(true, '加载分类列表...');
            
            // 加载分类数据
            await this.loadCategories();
            
            // 渲染页面内容
            this.renderContent();
            
            // 设置事件监听器
            this.setupEventListeners();
            
        } catch (error) {
            console.error('Categories page render error:', error);
            this.renderError(error);
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 加载分类列表
    async loadCategories() {
        try {
            this.categories = await api.getCategories();
        } catch (error) {
            console.error('Failed to load categories:', error);
            throw error;
        }
    }
    
    // 渲染页面内容
    renderContent() {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;
        
        adminContent.innerHTML = `
            <div class="categories-page">
                <div class="page-header">
                    <div class="page-title">
                        <h1>分类管理</h1>
                        <span class="page-subtitle">共 ${this.categories.length} 个分类</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="newCategoryBtn">
                            <i class="fas fa-plus"></i>
                            新建分类
                        </button>
                    </div>
                </div>
                
                <!-- 分类列表 -->
                ${this.renderCategoriesList()}
                
                <!-- 分类编辑模态框 -->
                ${this.renderCategoryModal()}
            </div>
        `;
    }
    
    // 渲染分类列表
    renderCategoriesList() {
        if (!this.categories || this.categories.length === 0) {
            return `
                <div class="no-categories">
                    <div class="no-content">
                        <i class="fas fa-folder-open"></i>
                        <h3>暂无分类</h3>
                        <p>还没有创建任何分类，点击上方按钮开始创建第一个分类。</p>
                        <button class="btn btn-primary" onclick="document.getElementById('newCategoryBtn').click()">
                            <i class="fas fa-plus"></i>
                            创建分类
                        </button>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="categories-grid">
                ${this.categories.map(category => this.renderCategoryCard(category)).join('')}
            </div>
        `;
    }
    
    // 渲染分类卡片
    renderCategoryCard(category) {
        return `
            <div class="category-card" data-id="${category._id}">
                <div class="category-header">
                    <div class="category-info">
                        <h3 class="category-name">${category.name}</h3>
                        <p class="category-description">${category.description || '暂无描述'}</p>
                    </div>
                    <div class="category-actions">
                        <button class="btn btn-sm btn-outline-primary edit-btn" 
                                data-id="${category._id}" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-btn" 
                                data-id="${category._id}" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="category-stats">
                    <div class="stat-item">
                        <span class="stat-label">文章数量：</span>
                        <span class="stat-value">${Utils.formatNumber(category.articleCount || 0)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">创建时间：</span>
                        <span class="stat-value">${Utils.formatDate(category.createdAt, 'SHORT')}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最后更新：</span>
                        <span class="stat-value">${Utils.formatDate(category.updatedAt, 'RELATIVE')}</span>
                    </div>
                </div>
                
                <div class="category-footer">
                    <div class="category-slug">
                        <i class="fas fa-link"></i>
                        <code>/category/${category.slug || category._id}</code>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染分类编辑模态框
    renderCategoryModal() {
        return `
            <div class="modal fade" id="categoryModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="categoryModalTitle">新建分类</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="categoryForm">
                                <div class="form-group">
                                    <label for="categoryName" class="form-label">分类名称 *</label>
                                    <input type="text" id="categoryName" class="form-control" 
                                           placeholder="请输入分类名称" required maxlength="50">
                                    <div class="form-text">分类名称将显示在网站前台</div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="categorySlug" class="form-label">URL别名</label>
                                    <input type="text" id="categorySlug" class="form-control" 
                                           placeholder="自动生成或手动输入" maxlength="50">
                                    <div class="form-text">用于生成分类页面URL，如：/category/tech</div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="categoryDescription" class="form-label">分类描述</label>
                                    <textarea id="categoryDescription" class="form-control" 
                                              placeholder="请输入分类描述（可选）" rows="3" maxlength="200"></textarea>
                                    <div class="form-text">分类描述将显示在分类页面</div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="categoryColor" class="form-label">分类颜色</label>
                                    <div class="color-picker-group">
                                        <input type="color" id="categoryColor" class="form-control color-input" value="#007bff">
                                        <div class="color-presets">
                                            <button type="button" class="color-preset" data-color="#007bff" style="background: #007bff;"></button>
                                            <button type="button" class="color-preset" data-color="#28a745" style="background: #28a745;"></button>
                                            <button type="button" class="color-preset" data-color="#dc3545" style="background: #dc3545;"></button>
                                            <button type="button" class="color-preset" data-color="#ffc107" style="background: #ffc107;"></button>
                                            <button type="button" class="color-preset" data-color="#17a2b8" style="background: #17a2b8;"></button>
                                            <button type="button" class="color-preset" data-color="#6f42c1" style="background: #6f42c1;"></button>
                                            <button type="button" class="color-preset" data-color="#fd7e14" style="background: #fd7e14;"></button>
                                            <button type="button" class="color-preset" data-color="#20c997" style="background: #20c997;"></button>
                                        </div>
                                    </div>
                                    <div class="form-text">用于在前台显示分类标签的颜色</div>
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" id="categoryVisible" class="form-check-input" checked>
                                        <label for="categoryVisible" class="form-check-label">
                                            在前台显示此分类
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveCategoryBtn">
                                <i class="fas fa-save"></i>
                                保存分类
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 新建分类按钮
        const newCategoryBtn = document.getElementById('newCategoryBtn');
        if (newCategoryBtn) {
            newCategoryBtn.addEventListener('click', this.handleNewCategory.bind(this));
        }
        
        // 分类操作按钮
        document.addEventListener('click', this.handleCategoryActions.bind(this));
        
        // 分类名称输入自动生成slug
        const categoryName = document.getElementById('categoryName');
        if (categoryName) {
            categoryName.addEventListener('input', this.handleNameChange.bind(this));
        }
        
        // 颜色预设按钮
        document.addEventListener('click', this.handleColorPreset.bind(this));
        
        // 保存分类按钮
        const saveCategoryBtn = document.getElementById('saveCategoryBtn');
        if (saveCategoryBtn) {
            saveCategoryBtn.addEventListener('click', this.handleSaveCategory.bind(this));
        }
        
        // 表单提交
        const categoryForm = document.getElementById('categoryForm');
        if (categoryForm) {
            categoryForm.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
        
        // 模态框关闭事件
        const categoryModal = document.getElementById('categoryModal');
        if (categoryModal) {
            categoryModal.addEventListener('hidden.bs.modal', this.handleModalClose.bind(this));
        }
    }
    
    // 处理新建分类
    handleNewCategory() {
        this.editingCategory = null;
        this.showCategoryModal();
    }
    
    // 处理分类操作
    async handleCategoryActions(event) {
        const editBtn = event.target.closest('.edit-btn');
        const deleteBtn = event.target.closest('.delete-btn');
        
        if (editBtn) {
            const categoryId = editBtn.dataset.id;
            await this.handleEditCategory(categoryId);
        } else if (deleteBtn) {
            const categoryId = deleteBtn.dataset.id;
            await this.handleDeleteCategory(categoryId);
        }
    }
    
    // 处理编辑分类
    async handleEditCategory(categoryId) {
        const category = this.categories.find(c => c._id === categoryId);
        if (!category) return;
        
        this.editingCategory = category;
        this.showCategoryModal(category);
    }
    
    // 处理删除分类
    async handleDeleteCategory(categoryId) {
        const category = this.categories.find(c => c._id === categoryId);
        if (!category) return;
        
        const hasArticles = category.articleCount > 0;
        const confirmMessage = hasArticles 
            ? `分类"${category.name}"下有 ${category.articleCount} 篇文章，删除后这些文章将变为未分类。确定要删除吗？`
            : `确定要删除分类"${category.name}"吗？`;
        
        const confirmed = confirm(confirmMessage);
        if (!confirmed) return;
        
        try {
            appState.setLoading(true, '删除分类中...');
            
            await api.deleteCategory(categoryId);
            
            appState.addToast({
                type: 'success',
                message: '分类删除成功'
            });
            
            // 重新加载分类列表
            await this.loadAndRender();
            
        } catch (error) {
            console.error('Delete category error:', error);
            appState.addToast({
                type: 'error',
                message: `删除失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 显示分类模态框
    showCategoryModal(category = null) {
        const modal = document.getElementById('categoryModal');
        const modalTitle = document.getElementById('categoryModalTitle');
        const form = document.getElementById('categoryForm');
        
        if (!modal || !modalTitle || !form) return;
        
        // 设置标题
        modalTitle.textContent = category ? '编辑分类' : '新建分类';
        
        // 填充表单数据
        if (category) {
            document.getElementById('categoryName').value = category.name || '';
            document.getElementById('categorySlug').value = category.slug || '';
            document.getElementById('categoryDescription').value = category.description || '';
            document.getElementById('categoryColor').value = category.color || '#007bff';
            document.getElementById('categoryVisible').checked = category.visible !== false;
        } else {
            form.reset();
            document.getElementById('categoryColor').value = '#007bff';
            document.getElementById('categoryVisible').checked = true;
        }
        
        // 显示模态框
        this.isModalOpen = true;
        modal.style.display = 'block';
        modal.classList.add('show');
        document.body.classList.add('modal-open');
        
        // 聚焦到名称输入框
        setTimeout(() => {
            document.getElementById('categoryName')?.focus();
        }, 100);
    }
    
    // 隐藏分类模态框
    hideCategoryModal() {
        const modal = document.getElementById('categoryModal');
        if (!modal) return;
        
        this.isModalOpen = false;
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }
    
    // 处理名称变化（自动生成slug）
    handleNameChange(event) {
        const name = event.target.value;
        const slugInput = document.getElementById('categorySlug');
        
        if (slugInput && !this.editingCategory) {
            // 只在新建时自动生成slug
            const slug = this.generateSlug(name);
            slugInput.value = slug;
        }
    }
    
    // 生成slug
    generateSlug(name) {
        return name
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // 移除特殊字符
            .replace(/\s+/g, '-') // 空格替换为连字符
            .replace(/-+/g, '-') // 多个连字符合并为一个
            .trim('-'); // 移除首尾连字符
    }
    
    // 处理颜色预设
    handleColorPreset(event) {
        const colorPreset = event.target.closest('.color-preset');
        if (!colorPreset) return;
        
        const color = colorPreset.dataset.color;
        const colorInput = document.getElementById('categoryColor');
        if (colorInput) {
            colorInput.value = color;
        }
    }
    
    // 处理保存分类
    async handleSaveCategory() {
        const form = document.getElementById('categoryForm');
        if (!form || !form.checkValidity()) {
            form?.reportValidity();
            return;
        }
        
        const categoryData = {
            name: document.getElementById('categoryName').value.trim(),
            slug: document.getElementById('categorySlug').value.trim(),
            description: document.getElementById('categoryDescription').value.trim(),
            color: document.getElementById('categoryColor').value,
            visible: document.getElementById('categoryVisible').checked
        };
        
        // 验证数据
        if (!categoryData.name) {
            appState.addToast({
                type: 'error',
                message: '请输入分类名称'
            });
            return;
        }
        
        // 如果没有slug，自动生成
        if (!categoryData.slug) {
            categoryData.slug = this.generateSlug(categoryData.name);
        }
        
        try {
            appState.setLoading(true, this.editingCategory ? '更新分类中...' : '创建分类中...');
            
            if (this.editingCategory) {
                await api.updateCategory(this.editingCategory._id, categoryData);
                appState.addToast({
                    type: 'success',
                    message: '分类更新成功'
                });
            } else {
                await api.createCategory(categoryData);
                appState.addToast({
                    type: 'success',
                    message: '分类创建成功'
                });
            }
            
            // 隐藏模态框
            this.hideCategoryModal();
            
            // 重新加载分类列表
            await this.loadAndRender();
            
        } catch (error) {
            console.error('Save category error:', error);
            appState.addToast({
                type: 'error',
                message: `保存失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 处理表单提交
    handleFormSubmit(event) {
        event.preventDefault();
        this.handleSaveCategory();
    }
    
    // 处理模态框关闭
    handleModalClose() {
        this.editingCategory = null;
        this.isModalOpen = false;
    }
    
    // 加载并重新渲染
    async loadAndRender() {
        try {
            await this.loadCategories();
            this.renderContent();
            this.setupEventListeners();
        } catch (error) {
            console.error('Load and render error:', error);
            this.renderError(error);
        }
    }
    
    // 渲染错误页面
    renderError(error) {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;
        
        adminContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>分类列表加载失败</h2>
                    <p>${error.message || '无法加载分类列表'}</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
            </div>
        `;
    }
}

// 创建分类管理实例
const adminCategories = new AdminCategories();

// 导出渲染函数
export const render = (params, query) => adminCategories.render(params, query);

// 导出默认实例
export default adminCategories;
