<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代博客 - Modern Blog</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-blog"></i>
                <span>现代博客</span>
            </div>
            
            <div class="nav-menu" id="navMenu">
                <a href="#home" class="nav-link active">首页</a>
                <a href="#articles" class="nav-link">文章</a>
                <a href="#categories" class="nav-link">分类</a>
                <a href="#about" class="nav-link">关于</a>
                <a href="#search" class="nav-link">搜索</a>
            </div>
            
            <div class="nav-actions">
                <button class="btn-search" id="searchToggle">
                    <i class="fas fa-search"></i>
                </button>
                <button class="btn-auth" id="authToggle">
                    <i class="fas fa-user"></i>
                </button>
                <button class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- 搜索覆盖层 -->
    <div class="search-overlay" id="searchOverlay">
        <div class="search-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索文章..." id="searchInput">
                <button class="search-close" id="searchClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-suggestions" id="searchSuggestions">
                <!-- 搜索建议将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 首页英雄区域 -->
        <section class="hero-section" id="home">
            <div class="hero-background">
                <div class="hero-overlay"></div>
            </div>
            <div class="hero-content">
                <h1 class="hero-title">分享知识，传递价值</h1>
                <p class="hero-subtitle">探索技术世界，记录成长足迹</p>
                <div class="hero-actions">
                    <button class="btn-primary" onclick="scrollToSection('articles')">
                        <i class="fas fa-book-open"></i>
                        开始阅读
                    </button>
                    <button class="btn-secondary" onclick="scrollToSection('about')">
                        <i class="fas fa-info-circle"></i>
                        了解更多
                    </button>
                </div>
            </div>
            <div class="hero-scroll-indicator">
                <i class="fas fa-chevron-down"></i>
            </div>
        </section>

        <!-- 特色文章区域 -->
        <section class="featured-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">精选文章</h2>
                    <p class="section-subtitle">最受欢迎的技术分享</p>
                </div>
                <div class="featured-grid" id="featuredArticles">
                    <!-- 特色文章将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 文章列表区域 -->
        <section class="articles-section" id="articles">
            <div class="container">
                <div class="blog-layout">
                    <!-- 主内容区域 -->
                    <main class="blog-main">
                        <div class="section-header">
                            <h2 class="section-title">最新文章</h2>
                            <div class="section-filters">
                                <select class="filter-select" id="categoryFilter">
                                    <option value="">所有分类</option>
                                </select>
                                <select class="filter-select" id="sortFilter">
                                    <option value="date">按时间排序</option>
                                    <option value="views">按浏览量排序</option>
                                    <option value="comments">按评论数排序</option>
                                </select>
                            </div>
                        </div>
                        <div class="articles-grid" id="articlesGrid">
                            <!-- 文章列表将在这里动态生成 -->
                        </div>
                        <div class="pagination" id="pagination">
                            <!-- 分页控件将在这里动态生成 -->
                        </div>
                    </main>

                    <!-- 侧边栏 -->
                    <aside class="blog-sidebar">
                        <!-- 搜索框 -->
                        <div class="sidebar-widget">
                            <h3 class="widget-title">搜索文章</h3>
                            <div class="search-widget">
                                <input type="text" id="sidebarSearch" placeholder="输入关键词搜索..." class="search-input">
                                <button class="search-btn" onclick="App.handleSidebarSearch()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 热门文章 -->
                        <div class="sidebar-widget">
                            <h3 class="widget-title">热门文章</h3>
                            <div id="popularArticles" class="popular-articles">
                                <!-- 热门文章将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 标签云 -->
                        <div class="sidebar-widget">
                            <h3 class="widget-title">标签云</h3>
                            <div id="tagCloud" class="tag-cloud">
                                <!-- 标签云将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 最新评论 -->
                        <div class="sidebar-widget">
                            <h3 class="widget-title">最新评论</h3>
                            <div id="recentComments" class="recent-comments">
                                <!-- 最新评论将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 归档 -->
                        <div class="sidebar-widget">
                            <h3 class="widget-title">文章归档</h3>
                            <div id="archiveList" class="archive-list">
                                <!-- 归档列表将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </section>

        <!-- 分类展示区域 -->
        <section class="categories-section" id="categories">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">文章分类</h2>
                    <p class="section-subtitle">按主题浏览内容</p>
                </div>
                <div class="categories-grid" id="categoriesGrid">
                    <!-- 分类卡片将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 关于区域 -->
        <section class="about-section" id="about">
            <div class="container">
                <div class="about-content">
                    <div class="about-text">
                        <h2 class="section-title">关于博客</h2>
                        <p>这是一个现代化的技术博客平台，致力于分享高质量的技术内容和编程经验。我们相信知识的力量，通过分享让更多人受益。</p>
                        <div class="about-features">
                            <div class="feature-item">
                                <i class="fas fa-rocket"></i>
                                <h4>现代化设计</h4>
                                <p>采用最新的设计理念，提供优雅的用户体验</p>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-search"></i>
                                <h4>强大搜索</h4>
                                <p>基于MongoDB的全文搜索，快速找到所需内容</p>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt"></i>
                                <h4>响应式设计</h4>
                                <p>完美适配各种设备，随时随地阅读</p>
                            </div>
                        </div>
                    </div>
                    <div class="about-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalArticles">0</div>
                            <div class="stat-label">文章总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="totalViews">0</div>
                            <div class="stat-label">总浏览量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="totalComments">0</div>
                            <div class="stat-label">评论总数</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <i class="fas fa-blog"></i>
                    <span>现代博客</span>
                </div>
                <div class="footer-links">
                    <a href="#home">首页</a>
                    <a href="#articles">文章</a>
                    <a href="#categories">分类</a>
                    <a href="#about">关于</a>
                </div>
                <div class="footer-social">
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 现代博客. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- 认证模态框 -->
    <div class="modal" id="authModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="authModalTitle">登录</h3>
                <button class="modal-close" id="authModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="authModalBody">
                <!-- 认证表单将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 文章详情模态框 -->
    <div class="modal" id="articleModal">
        <div class="modal-content article-modal-content">
            <div class="modal-header">
                <button class="modal-close" id="articleModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="articleModalBody">
                <!-- 文章详情将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="scripts/config.js"></script>
    <script src="scripts/api.js"></script>
    <script src="scripts/auth.js"></script>
    <script src="scripts/ui.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>
