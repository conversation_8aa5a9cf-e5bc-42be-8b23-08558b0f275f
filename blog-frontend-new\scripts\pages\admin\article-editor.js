import { api } from '../../api.js';
import { appState } from '../../state.js';
import { router } from '../../router.js';
import { CONFIG } from '../../config.js';
import { Utils } from '../../utils.js';

// 管理后台文章编辑器组件
export class AdminArticleEditor {
    constructor() {
        this.article = null;
        this.categories = [];
        this.tags = [];
        this.isEditing = false;
        this.articleId = null;
        this.isDirty = false;
        this.autoSaveInterval = null;
        this.editor = null;
    }

    // 渲染文章编辑器页面
    async render(params = {}, query = {}) {
        try {
            // 显示加载状态
            appState.setLoading(true, '加载编辑器...');

            // 判断是编辑还是新建
            this.articleId = params.id;
            this.isEditing = !!this.articleId;

            // 并行加载数据
            await Promise.all([
                this.loadCategories(),
                this.loadTags(),
                this.isEditing ? this.loadArticle(this.articleId) : this.initNewArticle()
            ]);

            // 渲染页面内容
            this.renderContent();

            // 初始化编辑器
            this.initEditor();

            // 设置事件监听器
            this.setupEventListeners();

            // 设置自动保存
            this.setupAutoSave();

            // 设置页面离开提醒
            this.setupBeforeUnload();

        } catch (error) {
            console.error('Article editor render error:', error);
            this.renderError(error);
        } finally {
            appState.setLoading(false);
        }
    }

    // 加载文章数据
    async loadArticle(articleId) {
        try {
            this.article = await api.getArticle(articleId);
        } catch (error) {
            console.error('Failed to load article:', error);
            throw error;
        }
    }

    // 初始化新文章
    initNewArticle() {
        this.article = {
            title: '',
            content: '',
            excerpt: '',
            category: '',
            tags: [],
            status: 'draft',
            featuredImage: '',
            seoTitle: '',
            seoDescription: '',
            seoKeywords: ''
        };
    }

    // 加载分类列表
    async loadCategories() {
        try {
            this.categories = await api.getCategories();
        } catch (error) {
            console.error('Failed to load categories:', error);
            this.categories = [];
        }
    }

    // 加载标签列表
    async loadTags() {
        try {
            this.tags = await api.getTags();
        } catch (error) {
            console.error('Failed to load tags:', error);
            this.tags = [];
        }
    }

    // 渲染页面内容
    renderContent() {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;

        adminContent.innerHTML = `
            <div class="article-editor-page">
                <div class="editor-header">
                    <div class="editor-title">
                        <h1>${this.isEditing ? '编辑文章' : '新建文章'}</h1>
                        <div class="editor-status">
                            <span class="save-status" id="saveStatus">已保存</span>
                            <span class="word-count" id="wordCount">0 字</span>
                        </div>
                    </div>
                    <div class="editor-actions">
                        <button class="btn btn-secondary" id="previewBtn">
                            <i class="fas fa-eye"></i>
                            预览
                        </button>
                        <button class="btn btn-outline-secondary" id="saveDraftBtn">
                            <i class="fas fa-save"></i>
                            保存草稿
                        </button>
                        <button class="btn btn-success" id="publishBtn">
                            <i class="fas fa-paper-plane"></i>
                            ${this.article?.status === 'published' ? '更新' : '发布'}
                        </button>
                        <button class="btn btn-outline-danger" id="cancelBtn">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                    </div>
                </div>

                <div class="editor-content">
                    <div class="row">
                        <!-- 主编辑区域 -->
                        <div class="col-lg-8 col-md-12">
                            <div class="editor-main">
                                ${this.renderMainEditor()}
                            </div>
                        </div>

                        <!-- 侧边栏设置 -->
                        <div class="col-lg-4 col-md-12">
                            <div class="editor-sidebar">
                                ${this.renderSidebarSettings()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染主编辑器
    renderMainEditor() {
        return `
            <div class="main-editor">
                <!-- 标题输入 -->
                <div class="title-section">
                    <input type="text" id="articleTitle" class="title-input"
                           placeholder="请输入文章标题..."
                           value="${this.article?.title || ''}"
                           maxlength="200">
                    <div class="title-counter">
                        <span id="titleCounter">${this.article?.title?.length || 0}</span>/200
                    </div>
                </div>

                <!-- 摘要输入 -->
                <div class="excerpt-section">
                    <label class="form-label">文章摘要</label>
                    <textarea id="articleExcerpt" class="excerpt-input"
                              placeholder="请输入文章摘要（可选，如不填写将自动从内容中提取）..."
                              rows="3" maxlength="500">${this.article?.excerpt || ''}</textarea>
                    <div class="excerpt-counter">
                        <span id="excerptCounter">${this.article?.excerpt?.length || 0}</span>/500
                    </div>
                </div>

                <!-- 内容编辑器 -->
                <div class="content-section">
                    <label class="form-label">文章内容</label>
                    <div class="editor-toolbar">
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="underline" title="下划线">
                                <i class="fas fa-underline"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="strikethrough" title="删除线">
                                <i class="fas fa-strikethrough"></i>
                            </button>
                        </div>
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" data-action="heading" title="标题">
                                <i class="fas fa-heading"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="quote" title="引用">
                                <i class="fas fa-quote-left"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="code" title="代码">
                                <i class="fas fa-code"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="link" title="链接">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" data-action="list-ul" title="无序列表">
                                <i class="fas fa-list-ul"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="list-ol" title="有序列表">
                                <i class="fas fa-list-ol"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="image" title="插入图片">
                                <i class="fas fa-image"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="table" title="插入表格">
                                <i class="fas fa-table"></i>
                            </button>
                        </div>
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" data-action="fullscreen" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="editor-container">
                        <textarea id="articleContent" class="content-editor"
                                  placeholder="开始写作...">${this.article?.content || ''}</textarea>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染侧边栏设置
    renderSidebarSettings() {
        return `
            <div class="sidebar-settings">
                <!-- 发布设置 -->
                <div class="settings-card">
                    <h3 class="card-title">发布设置</h3>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select id="articleStatus" class="form-select">
                                <option value="draft" ${this.article?.status === 'draft' ? 'selected' : ''}>草稿</option>
                                <option value="published" ${this.article?.status === 'published' ? 'selected' : ''}>已发布</option>
                                <option value="archived" ${this.article?.status === 'archived' ? 'selected' : ''}>已归档</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">分类</label>
                            <select id="articleCategory" class="form-select">
                                <option value="">选择分类</option>
                                ${this.categories.map(category => `
                                    <option value="${category._id}" ${this.article?.category === category._id ? 'selected' : ''}>
                                        ${category.name}
                                    </option>
                                `).join('')}
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">标签</label>
                            <div class="tags-input-container">
                                <input type="text" id="tagsInput" class="form-control"
                                       placeholder="输入标签，按回车添加">
                                <div class="tags-list" id="tagsList">
                                    ${(this.article?.tags || []).map(tag => `
                                        <span class="tag-item">
                                            ${tag}
                                            <button type="button" class="tag-remove" data-tag="${tag}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </span>
                                    `).join('')}
                                </div>
                                <div class="tags-suggestions" id="tagsSuggestions"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 特色图片 -->
                <div class="settings-card">
                    <h3 class="card-title">特色图片</h3>
                    <div class="card-body">
                        <div class="featured-image-section">
                            <div class="image-preview" id="imagePreview">
                                ${this.article?.featuredImage ? `
                                    <img src="${this.article.featuredImage}" alt="特色图片">
                                    <div class="image-overlay">
                                        <button type="button" class="btn btn-sm btn-danger" id="removeImageBtn">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                ` : `
                                    <div class="no-image">
                                        <i class="fas fa-image"></i>
                                        <p>暂无特色图片</p>
                                    </div>
                                `}
                            </div>
                            <div class="image-actions">
                                <input type="file" id="imageUpload" accept="image/*" style="display: none;">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="uploadImageBtn">
                                    <i class="fas fa-upload"></i>
                                    上传图片
                                </button>
                                <input type="url" id="imageUrl" class="form-control mt-2"
                                       placeholder="或输入图片URL"
                                       value="${this.article?.featuredImage || ''}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO设置 -->
                <div class="settings-card">
                    <h3 class="card-title">SEO设置</h3>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">SEO标题</label>
                            <input type="text" id="seoTitle" class="form-control"
                                   placeholder="SEO标题（可选）"
                                   value="${this.article?.seoTitle || ''}"
                                   maxlength="60">
                            <small class="form-text">建议长度：50-60个字符</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">SEO描述</label>
                            <textarea id="seoDescription" class="form-control"
                                      placeholder="SEO描述（可选）"
                                      rows="3" maxlength="160">${this.article?.seoDescription || ''}</textarea>
                            <small class="form-text">建议长度：150-160个字符</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">SEO关键词</label>
                            <input type="text" id="seoKeywords" class="form-control"
                                   placeholder="关键词，用逗号分隔"
                                   value="${this.article?.seoKeywords || ''}">
                        </div>
                    </div>
                </div>

                <!-- 文章统计 -->
                ${this.isEditing ? this.renderArticleStats() : ''}
            </div>
        `;
    }

    // 渲染文章统计
    renderArticleStats() {
        if (!this.article) return '';

        return `
            <div class="settings-card">
                <h3 class="card-title">文章统计</h3>
                <div class="card-body">
                    <div class="stats-list">
                        <div class="stat-item">
                            <span class="stat-label">创建时间：</span>
                            <span class="stat-value">${Utils.formatDate(this.article.createdAt, 'FULL')}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最后更新：</span>
                            <span class="stat-value">${Utils.formatDate(this.article.updatedAt, 'FULL')}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">浏览量：</span>
                            <span class="stat-value">${Utils.formatNumber(this.article.views || 0)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">评论数：</span>
                            <span class="stat-value">${Utils.formatNumber(this.article.commentCount || 0)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">点赞数：</span>
                            <span class="stat-value">${Utils.formatNumber(this.article.likes || 0)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 初始化编辑器
    initEditor() {
        const contentEditor = document.getElementById('articleContent');
        if (!contentEditor) return;

        // 这里可以集成专业的Markdown编辑器，如SimpleMDE、CodeMirror等
        // 暂时使用简单的textarea
        this.editor = contentEditor;

        // 设置编辑器事件
        this.editor.addEventListener('input', this.handleContentChange.bind(this));
        this.editor.addEventListener('keydown', this.handleEditorKeydown.bind(this));
    }

    // 设置事件监听器
    setupEventListeners() {
        // 标题输入
        const titleInput = document.getElementById('articleTitle');
        if (titleInput) {
            titleInput.addEventListener('input', this.handleTitleChange.bind(this));
        }

        // 摘要输入
        const excerptInput = document.getElementById('articleExcerpt');
        if (excerptInput) {
            excerptInput.addEventListener('input', this.handleExcerptChange.bind(this));
        }

        // 工具栏按钮
        document.addEventListener('click', this.handleToolbarActions.bind(this));

        // 标签输入
        const tagsInput = document.getElementById('tagsInput');
        if (tagsInput) {
            tagsInput.addEventListener('keypress', this.handleTagInput.bind(this));
            tagsInput.addEventListener('input', this.handleTagSuggestions.bind(this));
        }

        // 标签删除
        document.addEventListener('click', this.handleTagRemove.bind(this));

        // 图片上传
        const uploadImageBtn = document.getElementById('uploadImageBtn');
        const imageUpload = document.getElementById('imageUpload');
        const imageUrl = document.getElementById('imageUrl');
        const removeImageBtn = document.getElementById('removeImageBtn');

        if (uploadImageBtn) {
            uploadImageBtn.addEventListener('click', () => imageUpload?.click());
        }
        if (imageUpload) {
            imageUpload.addEventListener('change', this.handleImageUpload.bind(this));
        }
        if (imageUrl) {
            imageUrl.addEventListener('input', this.handleImageUrlChange.bind(this));
        }
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', this.handleRemoveImage.bind(this));
        }

        // 表单字段变化
        document.addEventListener('change', this.handleFormChange.bind(this));

        // 操作按钮
        document.getElementById('previewBtn')?.addEventListener('click', this.handlePreview.bind(this));
        document.getElementById('saveDraftBtn')?.addEventListener('click', () => this.handleSave('draft'));
        document.getElementById('publishBtn')?.addEventListener('click', () => this.handleSave('published'));
        document.getElementById('cancelBtn')?.addEventListener('click', this.handleCancel.bind(this));
    }

    // 处理标题变化
    handleTitleChange(event) {
        const title = event.target.value;
        this.article.title = title;
        this.isDirty = true;
        this.updateSaveStatus('未保存');

        // 更新字符计数
        const counter = document.getElementById('titleCounter');
        if (counter) {
            counter.textContent = title.length;
        }

        // 自动生成SEO标题
        const seoTitle = document.getElementById('seoTitle');
        if (seoTitle && !seoTitle.value) {
            seoTitle.value = title.substring(0, 60);
        }
    }

    // 处理摘要变化
    handleExcerptChange(event) {
        const excerpt = event.target.value;
        this.article.excerpt = excerpt;
        this.isDirty = true;
        this.updateSaveStatus('未保存');

        // 更新字符计数
        const counter = document.getElementById('excerptCounter');
        if (counter) {
            counter.textContent = excerpt.length;
        }

        // 自动生成SEO描述
        const seoDescription = document.getElementById('seoDescription');
        if (seoDescription && !seoDescription.value) {
            seoDescription.value = excerpt.substring(0, 160);
        }
    }

    // 处理内容变化
    handleContentChange(event) {
        const content = event.target.value;
        this.article.content = content;
        this.isDirty = true;
        this.updateSaveStatus('未保存');

        // 更新字数统计
        this.updateWordCount(content);

        // 如果没有摘要，自动生成
        const excerptInput = document.getElementById('articleExcerpt');
        if (excerptInput && !excerptInput.value && content) {
            const autoExcerpt = content.replace(/[#*`]/g, '').substring(0, 200);
            excerptInput.value = autoExcerpt;
            this.article.excerpt = autoExcerpt;
        }
    }

    // 更新字数统计
    updateWordCount(content) {
        const wordCount = content.length;
        const wordCountElement = document.getElementById('wordCount');
        if (wordCountElement) {
            wordCountElement.textContent = `${Utils.formatNumber(wordCount)} 字`;
        }
    }

    // 处理编辑器快捷键
    handleEditorKeydown(event) {
        // Ctrl+S 保存
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            this.handleSave('draft');
        }

        // Tab键插入缩进
        if (event.key === 'Tab') {
            event.preventDefault();
            const start = event.target.selectionStart;
            const end = event.target.selectionEnd;
            const value = event.target.value;

            event.target.value = value.substring(0, start) + '    ' + value.substring(end);
            event.target.selectionStart = event.target.selectionEnd = start + 4;
        }
    }

    // 处理工具栏操作
    handleToolbarActions(event) {
        const toolbarBtn = event.target.closest('.toolbar-btn');
        if (!toolbarBtn) return;

        const action = toolbarBtn.dataset.action;
        this.executeEditorAction(action);
    }

    // 执行编辑器操作
    executeEditorAction(action) {
        const editor = this.editor;
        if (!editor) return;

        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        const selectedText = editor.value.substring(start, end);
        const beforeText = editor.value.substring(0, start);
        const afterText = editor.value.substring(end);

        let newText = '';
        let newCursorPos = start;

        switch (action) {
            case 'bold':
                newText = `**${selectedText || '粗体文本'}**`;
                newCursorPos = start + (selectedText ? newText.length : 2);
                break;
            case 'italic':
                newText = `*${selectedText || '斜体文本'}*`;
                newCursorPos = start + (selectedText ? newText.length : 1);
                break;
            case 'heading':
                newText = `## ${selectedText || '标题'}`;
                newCursorPos = start + (selectedText ? newText.length : 3);
                break;
            case 'quote':
                newText = `> ${selectedText || '引用文本'}`;
                newCursorPos = start + (selectedText ? newText.length : 2);
                break;
            case 'code':
                if (selectedText.includes('\n')) {
                    newText = `\`\`\`\n${selectedText || '代码块'}\n\`\`\``;
                } else {
                    newText = `\`${selectedText || '代码'}\``;
                }
                newCursorPos = start + (selectedText ? newText.length : (newText.includes('\n') ? 4 : 1));
                break;
            case 'link':
                newText = `[${selectedText || '链接文本'}](URL)`;
                newCursorPos = start + newText.length - 4;
                break;
            case 'list-ul':
                newText = `- ${selectedText || '列表项'}`;
                newCursorPos = start + (selectedText ? newText.length : 2);
                break;
            case 'list-ol':
                newText = `1. ${selectedText || '列表项'}`;
                newCursorPos = start + (selectedText ? newText.length : 3);
                break;
            case 'image':
                newText = `![${selectedText || '图片描述'}](图片URL)`;
                newCursorPos = start + newText.length - 5;
                break;
            case 'table':
                newText = `| 标题1 | 标题2 | 标题3 |\n|-------|-------|-------|\n| 内容1 | 内容2 | 内容3 |`;
                newCursorPos = start + newText.length;
                break;
            case 'fullscreen':
                this.toggleFullscreen();
                return;
        }

        if (newText) {
            editor.value = beforeText + newText + afterText;
            editor.focus();
            editor.setSelectionRange(newCursorPos, newCursorPos);

            // 触发内容变化事件
            this.handleContentChange({ target: editor });
        }
    }

    // 切换全屏模式
    toggleFullscreen() {
        const editorPage = document.querySelector('.article-editor-page');
        const fullscreenBtn = document.querySelector('[data-action="fullscreen"] i');

        if (editorPage) {
            editorPage.classList.toggle('fullscreen');
            if (fullscreenBtn) {
                fullscreenBtn.className = editorPage.classList.contains('fullscreen')
                    ? 'fas fa-compress'
                    : 'fas fa-expand';
            }
        }
    }

    // 处理标签输入
    handleTagInput(event) {
        if (event.key === 'Enter' || event.key === ',') {
            event.preventDefault();
            const input = event.target;
            const tag = input.value.trim();

            if (tag && !this.article.tags.includes(tag)) {
                this.article.tags.push(tag);
                this.renderTagsList();
                input.value = '';
                this.isDirty = true;
                this.updateSaveStatus('未保存');
            }
        }
    }

    // 处理标签建议
    handleTagSuggestions(event) {
        const input = event.target.value.toLowerCase();
        if (!input) {
            this.hideSuggestions();
            return;
        }

        const suggestions = this.tags
            .filter(tag => tag.toLowerCase().includes(input) && !this.article.tags.includes(tag))
            .slice(0, 5);

        this.showSuggestions(suggestions);
    }

    // 显示标签建议
    showSuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('tagsSuggestions');
        if (!suggestionsContainer) return;

        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        suggestionsContainer.innerHTML = suggestions.map(tag => `
            <div class="suggestion-item" data-tag="${tag}">
                ${tag}
            </div>
        `).join('');

        suggestionsContainer.style.display = 'block';

        // 添加点击事件
        suggestionsContainer.addEventListener('click', this.handleSuggestionClick.bind(this));
    }

    // 隐藏标签建议
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('tagsSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    // 处理建议点击
    handleSuggestionClick(event) {
        const suggestionItem = event.target.closest('.suggestion-item');
        if (!suggestionItem) return;

        const tag = suggestionItem.dataset.tag;
        if (tag && !this.article.tags.includes(tag)) {
            this.article.tags.push(tag);
            this.renderTagsList();
            document.getElementById('tagsInput').value = '';
            this.hideSuggestions();
            this.isDirty = true;
            this.updateSaveStatus('未保存');
        }
    }

    // 渲染标签列表
    renderTagsList() {
        const tagsList = document.getElementById('tagsList');
        if (!tagsList) return;

        tagsList.innerHTML = this.article.tags.map(tag => `
            <span class="tag-item">
                ${tag}
                <button type="button" class="tag-remove" data-tag="${tag}">
                    <i class="fas fa-times"></i>
                </button>
            </span>
        `).join('');
    }

    // 处理标签删除
    handleTagRemove(event) {
        const removeBtn = event.target.closest('.tag-remove');
        if (!removeBtn) return;

        const tag = removeBtn.dataset.tag;
        const index = this.article.tags.indexOf(tag);
        if (index > -1) {
            this.article.tags.splice(index, 1);
            this.renderTagsList();
            this.isDirty = true;
            this.updateSaveStatus('未保存');
        }
    }

    // 处理图片上传
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            appState.addToast({
                type: 'error',
                message: '请选择图片文件'
            });
            return;
        }

        // 验证文件大小（5MB）
        if (file.size > 5 * 1024 * 1024) {
            appState.addToast({
                type: 'error',
                message: '图片文件不能超过5MB'
            });
            return;
        }

        try {
            appState.setLoading(true, '上传图片中...');

            const formData = new FormData();
            formData.append('image', file);

            const response = await api.uploadImage(formData);
            const imageUrl = response.url || response.data?.url;

            if (imageUrl) {
                this.article.featuredImage = imageUrl;
                document.getElementById('imageUrl').value = imageUrl;
                this.updateImagePreview(imageUrl);
                this.isDirty = true;
                this.updateSaveStatus('未保存');

                appState.addToast({
                    type: 'success',
                    message: '图片上传成功'
                });
            }

        } catch (error) {
            console.error('Image upload error:', error);
            appState.addToast({
                type: 'error',
                message: `图片上传失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }

    // 处理图片URL变化
    handleImageUrlChange(event) {
        const url = event.target.value.trim();
        this.article.featuredImage = url;
        this.updateImagePreview(url);
        this.isDirty = true;
        this.updateSaveStatus('未保存');
    }

    // 更新图片预览
    updateImagePreview(url) {
        const imagePreview = document.getElementById('imagePreview');
        if (!imagePreview) return;

        if (url) {
            imagePreview.innerHTML = `
                <img src="${url}" alt="特色图片">
                <div class="image-overlay">
                    <button type="button" class="btn btn-sm btn-danger" id="removeImageBtn">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            // 重新绑定删除按钮事件
            const removeBtn = imagePreview.querySelector('#removeImageBtn');
            if (removeBtn) {
                removeBtn.addEventListener('click', this.handleRemoveImage.bind(this));
            }
        } else {
            imagePreview.innerHTML = `
                <div class="no-image">
                    <i class="fas fa-image"></i>
                    <p>暂无特色图片</p>
                </div>
            `;
        }
    }

    // 处理删除图片
    handleRemoveImage() {
        this.article.featuredImage = '';
        document.getElementById('imageUrl').value = '';
        this.updateImagePreview('');
        this.isDirty = true;
        this.updateSaveStatus('未保存');
    }

    // 处理表单字段变化
    handleFormChange(event) {
        const field = event.target;
        const fieldName = field.id;

        switch (fieldName) {
            case 'articleStatus':
                this.article.status = field.value;
                break;
            case 'articleCategory':
                this.article.category = field.value;
                break;
            case 'seoTitle':
                this.article.seoTitle = field.value;
                break;
            case 'seoDescription':
                this.article.seoDescription = field.value;
                break;
            case 'seoKeywords':
                this.article.seoKeywords = field.value;
                break;
        }

        if (fieldName.startsWith('article') || fieldName.startsWith('seo')) {
            this.isDirty = true;
            this.updateSaveStatus('未保存');
        }
    }

    // 处理预览
    handlePreview() {
        if (this.article.title && this.article.content) {
            // 在新窗口中打开预览
            const previewData = {
                title: this.article.title,
                content: this.article.content,
                excerpt: this.article.excerpt,
                featuredImage: this.article.featuredImage,
                category: this.categories.find(c => c._id === this.article.category)?.name || '未分类',
                tags: this.article.tags,
                createdAt: new Date().toISOString()
            };

            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(this.generatePreviewHTML(previewData));
        } else {
            appState.addToast({
                type: 'warning',
                message: '请先填写标题和内容'
            });
        }
    }

    // 生成预览HTML
    generatePreviewHTML(data) {
        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${data.title} - 预览</title>
                <style>
                    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
                    .preview-header { border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }
                    .preview-title { font-size: 2em; margin-bottom: 10px; }
                    .preview-meta { color: #666; font-size: 0.9em; }
                    .preview-image { width: 100%; max-width: 600px; height: auto; margin: 20px 0; }
                    .preview-content { font-size: 1.1em; line-height: 1.8; }
                    .preview-tags { margin-top: 30px; }
                    .tag { background: #f0f0f0; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-size: 0.9em; }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    <h1 class="preview-title">${data.title}</h1>
                    <div class="preview-meta">
                        分类：${data.category} | 创建时间：${Utils.formatDate(data.createdAt, 'FULL')}
                    </div>
                </div>
                ${data.featuredImage ? `<img src="${data.featuredImage}" alt="${data.title}" class="preview-image">` : ''}
                ${data.excerpt ? `<div class="preview-excerpt" style="font-style: italic; color: #666; margin-bottom: 20px;">${data.excerpt}</div>` : ''}
                <div class="preview-content">${this.formatPreviewContent(data.content)}</div>
                ${data.tags && data.tags.length > 0 ? `
                    <div class="preview-tags">
                        <strong>标签：</strong>
                        ${data.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                ` : ''}
            </body>
            </html>
        `;
    }

    // 格式化预览内容
    formatPreviewContent(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">$1</code>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^> (.*$)/gm, '<blockquote style="border-left: 4px solid #ddd; margin: 0; padding-left: 16px; color: #666;">$1</blockquote>');
    }

    // 处理保存
    async handleSave(status = 'draft') {
        if (!this.validateArticle()) return;

        try {
            appState.setLoading(true, status === 'published' ? '发布文章中...' : '保存草稿中...');

            const articleData = {
                title: this.article.title,
                content: this.article.content,
                excerpt: this.article.excerpt,
                category: this.article.category,
                tags: this.article.tags,
                status: status,
                featuredImage: this.article.featuredImage,
                seoTitle: this.article.seoTitle,
                seoDescription: this.article.seoDescription,
                seoKeywords: this.article.seoKeywords
            };

            let result;
            if (this.isEditing) {
                result = await api.updateArticle(this.articleId, articleData);
            } else {
                result = await api.createArticle(articleData);
                this.articleId = result._id || result.data?._id;
                this.isEditing = true;

                // 更新URL
                history.replaceState(null, null, `/admin/articles/edit/${this.articleId}`);
            }

            this.article = { ...this.article, ...articleData };
            this.isDirty = false;
            this.updateSaveStatus('已保存');

            appState.addToast({
                type: 'success',
                message: status === 'published' ? '文章发布成功' : '草稿保存成功'
            });

            // 如果是发布，询问是否查看文章
            if (status === 'published') {
                const viewArticle = confirm('文章发布成功！是否查看已发布的文章？');
                if (viewArticle) {
                    window.open(`/article/${this.articleId}`, '_blank');
                }
            }

        } catch (error) {
            console.error('Save error:', error);
            appState.addToast({
                type: 'error',
                message: `保存失败：${error.message}`
            });
        } finally {
            appState.setLoading(false);
        }
    }

    // 验证文章数据
    validateArticle() {
        const errors = [];

        if (!this.article.title?.trim()) {
            errors.push('请输入文章标题');
        }

        if (!this.article.content?.trim()) {
            errors.push('请输入文章内容');
        }

        if (this.article.title && this.article.title.length > 200) {
            errors.push('标题长度不能超过200个字符');
        }

        if (this.article.excerpt && this.article.excerpt.length > 500) {
            errors.push('摘要长度不能超过500个字符');
        }

        if (errors.length > 0) {
            appState.addToast({
                type: 'error',
                message: errors.join('；')
            });
            return false;
        }

        return true;
    }

    // 处理取消
    async handleCancel() {
        if (this.isDirty) {
            const confirmed = confirm('您有未保存的更改，确定要离开吗？');
            if (!confirmed) return;
        }

        router.navigate('/admin/articles');
    }

    // 更新保存状态
    updateSaveStatus(status) {
        const saveStatus = document.getElementById('saveStatus');
        if (saveStatus) {
            saveStatus.textContent = status;
            saveStatus.className = `save-status ${status === '已保存' ? 'saved' : 'unsaved'}`;
        }
    }

    // 设置自动保存
    setupAutoSave() {
        // 每30秒自动保存草稿
        this.autoSaveInterval = setInterval(() => {
            if (this.isDirty && this.article.title && this.article.content) {
                this.handleSave('draft').catch(error => {
                    console.error('Auto save error:', error);
                });
            }
        }, 30000);
    }

    // 设置页面离开提醒
    setupBeforeUnload() {
        window.addEventListener('beforeunload', (event) => {
            if (this.isDirty) {
                event.preventDefault();
                event.returnValue = '您有未保存的更改，确定要离开吗？';
                return event.returnValue;
            }
        });
    }

    // 清理资源
    destroy() {
        // 清除自动保存
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }

        // 移除事件监听器
        window.removeEventListener('beforeunload', this.setupBeforeUnload);
    }

    // 渲染错误页面
    renderError(error) {
        const adminContent = document.getElementById('adminContent');
        if (!adminContent) return;

        adminContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>编辑器加载失败</h2>
                    <p>${error.message || '无法加载文章编辑器'}</p>
                    <div class="error-actions">
                        <a href="/admin/articles" class="btn btn-primary" data-route="/admin/articles">
                            <i class="fas fa-arrow-left"></i> 返回文章列表
                        </a>
                        <button onclick="location.reload()" class="btn btn-secondary">
                            <i class="fas fa-refresh"></i> 重新加载
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

// 创建文章编辑器实例
const adminArticleEditor = new AdminArticleEditor();

// 导出渲染函数
export const render = (params, query) => adminArticleEditor.render(params, query);

// 导出默认实例
export default adminArticleEditor;