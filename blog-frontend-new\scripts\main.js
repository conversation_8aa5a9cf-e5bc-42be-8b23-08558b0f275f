import { CONFIG } from './config.js';
import { api } from './api.js';
import { appState } from './state.js';
import { router } from './router.js';
import { authManager } from './auth.js';
import { uiManager } from './ui.js';
import { Utils, eventBus } from './utils.js';

// 主应用类
class BlogApp {
    constructor() {
        this.initialized = false;
        this.components = new Map();
    }
    
    // 初始化应用
    async init() {
        try {
            console.log('Initializing Blog App...');
            
            // 设置全局错误处理
            this.setupErrorHandling();
            
            // 初始化认证状态
            await authManager.init();
            
            // 初始化路由
            router.init();
            
            // 初始化UI管理器
            uiManager.init();
            
            // 加载初始数据
            await this.loadInitialData();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 标记为已初始化
            this.initialized = true;
            
            console.log('Blog App initialized successfully');
            
            // 发送应用初始化完成事件
            eventBus.emit('appInitialized');
            
        } catch (error) {
            console.error('App initialization error:', error);
            this.handleInitError(error);
        }
    }
    
    // 设置全局错误处理
    setupErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            appState.addToast({
                type: 'error',
                message: '发生了一个错误，请刷新页面重试'
            });
        });
        
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('JavaScript error:', event.error);
            appState.addToast({
                type: 'error',
                message: '页面出现错误，请刷新页面重试'
            });
        });
    }
    
    // 加载初始数据
    async loadInitialData() {
        try {
            // 并行加载基础数据
            const promises = [
                this.loadCategories(),
                this.loadPopularArticles()
            ];
            
            await Promise.allSettled(promises);
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }
    
    // 加载分类数据
    async loadCategories() {
        try {
            const categories = await api.getCategories();
            appState.setCategories(categories);
        } catch (error) {
            console.error('Failed to load categories:', error);
        }
    }
    
    // 加载热门文章
    async loadPopularArticles() {
        try {
            const popularArticles = await api.getPopularPosts();
            // 存储到状态中供侧边栏使用
            appState.setState({ popularArticles });
        } catch (error) {
            console.error('Failed to load popular articles:', error);
        }
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听路由变化
        eventBus.on('routeChange', this.handleRouteChange.bind(this));
        
        // 监听状态变化
        appState.subscribe(this.handleStateChange.bind(this));
        
        // 监听网络状态变化
        window.addEventListener('online', this.handleOnline.bind(this));
        window.addEventListener('offline', this.handleOffline.bind(this));
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
    
    // 处理路由变化
    handleRouteChange(data) {
        const { route, params, query } = data;
        
        // 更新页面标题
        this.updatePageTitle(route, params);
        
        // 清除之前的错误状态
        appState.clearError();
        
        // 记录页面访问
        this.trackPageView(route.path, params);
    }
    
    // 更新页面标题
    updatePageTitle(route, params) {
        let title = '现代博客系统';
        
        switch (route.component) {
            case 'home':
                title = '首页 - 现代博客系统';
                break;
            case 'article':
                // 如果有文章标题，使用文章标题
                const currentArticle = appState.getState().currentArticle;
                if (currentArticle) {
                    title = `${currentArticle.title} - 现代博客系统`;
                } else {
                    title = '文章详情 - 现代博客系统';
                }
                break;
            case 'category':
                title = '分类 - 现代博客系统';
                break;
            case 'search':
                title = '搜索 - 现代博客系统';
                break;
            case 'admin-dashboard':
                title = '仪表板 - 管理后台';
                break;
            case 'admin-articles':
                title = '文章管理 - 管理后台';
                break;
            case 'admin-article-editor':
                title = params.id ? '编辑文章 - 管理后台' : '新建文章 - 管理后台';
                break;
        }
        
        document.title = title;
    }
    
    // 记录页面访问
    trackPageView(path, params) {
        // 这里可以集成分析工具
        console.log('Page view:', path, params);
    }
    
    // 处理状态变化
    handleStateChange(currentState, prevState) {
        // 处理认证状态变化
        if (currentState.isAuthenticated !== prevState.isAuthenticated) {
            this.handleAuthStateChange(currentState.isAuthenticated);
        }
        
        // 处理主题变化
        if (currentState.theme !== prevState.theme) {
            this.handleThemeChange(currentState.theme);
        }
    }
    
    // 处理认证状态变化
    handleAuthStateChange(isAuthenticated) {
        if (isAuthenticated) {
            // 用户登录后的处理
            this.onUserLogin();
        } else {
            // 用户登出后的处理
            this.onUserLogout();
        }
    }
    
    // 用户登录后的处理
    onUserLogin() {
        // 刷新需要认证的数据
        this.refreshAuthenticatedData();
    }
    
    // 用户登出后的处理
    onUserLogout() {
        // 清除敏感数据
        appState.clearCache();
    }
    
    // 刷新需要认证的数据
    async refreshAuthenticatedData() {
        // 如果在管理后台，刷新管理数据
        if (window.location.pathname.startsWith('/admin')) {
            eventBus.emit('refreshAdminData');
        }
    }
    
    // 处理主题变化
    handleThemeChange(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // 更新图表主题（如果有的话）
        eventBus.emit('themeChanged', theme);
    }
    
    // 处理网络连接
    handleOnline() {
        appState.addToast({
            type: 'success',
            message: '网络连接已恢复'
        });
        
        // 重新加载失败的请求
        this.retryFailedRequests();
    }
    
    // 处理网络断开
    handleOffline() {
        appState.addToast({
            type: 'warning',
            message: '网络连接已断开，部分功能可能无法使用'
        });
    }
    
    // 处理页面可见性变化
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时的处理
            this.onPageHidden();
        } else {
            // 页面显示时的处理
            this.onPageVisible();
        }
    }
    
    // 页面隐藏时的处理
    onPageHidden() {
        // 暂停不必要的操作
    }
    
    // 页面显示时的处理
    onPageVisible() {
        // 恢复操作，检查数据更新
        this.checkForUpdates();
    }
    
    // 检查数据更新
    async checkForUpdates() {
        // 检查是否有新的数据需要更新
        try {
            // 这里可以实现数据更新检查逻辑
        } catch (error) {
            console.error('Failed to check for updates:', error);
        }
    }
    
    // 重试失败的请求
    retryFailedRequests() {
        // 实现请求重试逻辑
    }
    
    // 处理初始化错误
    handleInitError(error) {
        // 显示错误页面
        const body = document.body;
        body.innerHTML = `
            <div class="init-error">
                <div class="error-content">
                    <h1>应用初始化失败</h1>
                    <p>抱歉，应用无法正常启动。</p>
                    <p class="error-message">${error.message}</p>
                    <button onclick="location.reload()" class="btn btn-primary">重新加载</button>
                </div>
            </div>
        `;
    }
    
    // 获取应用信息
    getAppInfo() {
        return {
            version: '1.0.0',
            initialized: this.initialized,
            currentRoute: router.getCurrentRoute(),
            state: appState.getState()
        };
    }
    
    // 销毁应用
    destroy() {
        // 清理事件监听器
        eventBus.off('routeChange');
        eventBus.off('appInitialized');
        
        // 清理状态
        appState.clearCache();
        
        // 标记为未初始化
        this.initialized = false;
        
        console.log('Blog App destroyed');
    }
}

// 创建应用实例
const app = new BlogApp();

// DOM加载完成后初始化应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        app.init();
    });
} else {
    app.init();
}

// 导出应用实例供调试使用
window.BlogApp = app;

// 导出应用类
export default BlogApp;
