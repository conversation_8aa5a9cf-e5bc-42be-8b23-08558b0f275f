<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .auth-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .auth-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }

        .auth-info {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .token-display {
            flex: 1;
            min-width: 300px;
        }

        .token-display label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }

        .token-display input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            background: #fff;
        }

        .api-section {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .api-section h3 {
            background: #e9ecef;
            padding: 15px 20px;
            margin: 0;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
        }

        .api-content {
            padding: 20px;
        }

        .api-group {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #f1f3f4;
            border-radius: 6px;
            background: #fafafa;
        }

        .api-group h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }

        .response-area h5 {
            margin-bottom: 10px;
            color: #495057;
        }

        .response-content {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-left-color: #28a745;
        }

        .error {
            border-left-color: #dc3545;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .auth-info {
                flex-direction: column;
            }
            
            .token-display {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 博客API测试工具</h1>
            <p>完整的博客后端API接口测试平台</p>
        </div>

        <div class="content">
            <!-- 快速操作工具栏 -->
            <div class="api-section">
                <h3>�️ 快速操作工具</h3>
                <div class="api-content">
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 20px;">
                        <button class="btn" onclick="runAutoTest()" style="background-color: #28a745; color: white;">🚀 运行自动测试</button>
                        <button class="btn" onclick="fillSampleData()" style="background-color: #17a2b8; color: white;">📝 填充示例数据</button>
                        <button class="btn" onclick="clearAllResponses()" style="background-color: #6c757d; color: white;">🧹 清除所有响应</button>
                        <button class="btn" onclick="testConnection()" style="background-color: #28a745; color: white;">🔗 测试连接</button>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <h5>💡 使用提示:</h5>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>自动测试</strong>: 自动执行完整的API测试流程，包括注册、登录、创建分类和文章等</li>
                            <li><strong>填充示例数据</strong>: 在表单中填充示例数据，方便手动测试</li>
                            <li><strong>清除响应</strong>: 清除所有API响应结果，保持页面整洁</li>
                            <li><strong>测试连接</strong>: 检查服务器是否正常运行</li>
                        </ul>
                    </div>
                    <div id="connectionResponse" class="response-area" style="display: none;">
                        <h5>连接测试结果:</h5>
                        <div class="response-content"></div>
                    </div>
                </div>
            </div>

            <!-- 认证区域 -->
            <div class="auth-section">
                <h3>🔐 用户认证</h3>
                <div class="auth-info">
                    <div class="token-display">
                        <label>当前Token:</label>
                        <input type="text" id="currentToken" placeholder="请先登录获取Token" readonly>
                    </div>
                    <div>
                        <button class="btn" onclick="clearToken()">清除Token</button>
                    </div>
                </div>
            </div>

            <!-- 用户认证API -->
            <div class="api-section">
                <h3>👤 用户认证API</h3>
                <div class="api-content">
                    <!-- 用户注册 -->
                    <div class="api-group">
                        <h4>用户注册</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>用户名:</label>
                                <input type="text" id="registerUsername" placeholder="输入用户名">
                            </div>
                            <div class="form-group">
                                <label>邮箱:</label>
                                <input type="email" id="registerEmail" placeholder="输入邮箱">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>密码:</label>
                                <input type="password" id="registerPassword" placeholder="输入密码">
                            </div>
                            <div class="form-group">
                                <label>角色:</label>
                                <select id="registerRole">
                                    <option value="user">普通用户</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn" onclick="register()">注册用户</button>
                        <div id="registerResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 用户登录 -->
                    <div class="api-group">
                        <h4>用户登录</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>邮箱:</label>
                                <input type="email" id="loginEmail" placeholder="输入邮箱">
                            </div>
                            <div class="form-group">
                                <label>密码:</label>
                                <input type="password" id="loginPassword" placeholder="输入密码">
                            </div>
                        </div>
                        <button class="btn" onclick="login()">登录</button>
                        <div id="loginResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取用户信息 -->
                    <div class="api-group">
                        <h4>获取当前用户信息</h4>
                        <button class="btn" onclick="getCurrentUser()">获取用户信息</button>
                        <div id="userInfoResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分类管理API -->
            <div class="api-section">
                <h3>📁 分类管理API</h3>
                <div class="api-content">
                    <!-- 创建分类 -->
                    <div class="api-group">
                        <h4>创建分类 (需要管理员权限)</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>分类名称:</label>
                                <input type="text" id="categoryName" placeholder="输入分类名称">
                            </div>
                            <div class="form-group">
                                <label>分类描述:</label>
                                <input type="text" id="categoryDescription" placeholder="输入分类描述">
                            </div>
                        </div>
                        <button class="btn" onclick="createCategory()">创建分类</button>
                        <div id="createCategoryResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取分类列表 -->
                    <div class="api-group">
                        <h4>获取分类列表</h4>
                        <button class="btn" onclick="getCategories()">获取所有分类</button>
                        <div id="categoriesResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文章管理API -->
            <div class="api-section">
                <h3>📝 文章管理API</h3>
                <div class="api-content">
                    <!-- 创建文章 -->
                    <div class="api-group">
                        <h4>创建文章</h4>
                        <div class="form-group">
                            <label>文章标题:</label>
                            <input type="text" id="articleTitle" placeholder="输入文章标题">
                        </div>
                        <div class="form-group">
                            <label>文章内容:</label>
                            <textarea id="articleContent" placeholder="输入文章内容" rows="4"></textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>分类ID:</label>
                                <input type="text" id="articleCategory" placeholder="输入分类ID">
                            </div>
                            <div class="form-group">
                                <label>标签 (逗号分隔):</label>
                                <input type="text" id="articleTags" placeholder="标签1,标签2,标签3">
                            </div>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="articlePublished" checked>
                            <label for="articlePublished">立即发布</label>
                        </div>
                        <br><br>
                        <button class="btn" onclick="createArticle()">创建文章</button>
                        <div id="createArticleResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取文章列表 -->
                    <div class="api-group">
                        <h4>获取文章列表</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>页码:</label>
                                <input type="number" id="articlePage" value="1" min="1">
                            </div>
                            <div class="form-group">
                                <label>每页数量:</label>
                                <input type="number" id="articleLimit" value="10" min="1" max="50">
                            </div>
                            <div class="form-group">
                                <label>分类筛选:</label>
                                <input type="text" id="articleCategoryFilter" placeholder="分类ID">
                            </div>
                            <div class="form-group">
                                <label>标签筛选:</label>
                                <input type="text" id="articleTagsFilter" placeholder="标签名">
                            </div>
                        </div>
                        <button class="btn" onclick="getArticles()">获取文章列表</button>
                        <button class="btn" onclick="getAllArticles()">获取所有文章(管理员)</button>
                        <div id="articlesResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取单篇文章 -->
                    <div class="api-group">
                        <h4>获取单篇文章</h4>
                        <div class="form-group">
                            <label>文章ID:</label>
                            <input type="text" id="singleArticleId" placeholder="输入文章ID">
                        </div>
                        <button class="btn" onclick="getSingleArticle()">获取文章详情</button>
                        <div id="singleArticleResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 更新文章 -->
                    <div class="api-group">
                        <h4>更新文章</h4>
                        <div class="form-group">
                            <label>文章ID:</label>
                            <input type="text" id="updateArticleId" placeholder="输入要更新的文章ID">
                        </div>
                        <div class="form-group">
                            <label>新标题:</label>
                            <input type="text" id="updateArticleTitle" placeholder="输入新标题">
                        </div>
                        <div class="form-group">
                            <label>新内容:</label>
                            <textarea id="updateArticleContent" placeholder="输入新内容" rows="3"></textarea>
                        </div>
                        <button class="btn" onclick="updateArticle()">更新文章</button>
                        <div id="updateArticleResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 删除文章 -->
                    <div class="api-group">
                        <h4>删除文章 (需要管理员权限)</h4>
                        <div class="form-group">
                            <label>文章ID:</label>
                            <input type="text" id="deleteArticleId" placeholder="输入要删除的文章ID">
                        </div>
                        <button class="btn btn-danger" onclick="deleteArticle()">删除文章</button>
                        <div id="deleteArticleResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评论管理API -->
            <div class="api-section">
                <h3>💬 评论管理API</h3>
                <div class="api-content">
                    <!-- 添加评论 -->
                    <div class="api-group">
                        <h4>添加评论</h4>
                        <div class="form-group">
                            <label>文章ID:</label>
                            <input type="text" id="commentArticleId" placeholder="输入文章ID">
                        </div>
                        <div class="form-group">
                            <label>评论内容:</label>
                            <textarea id="commentContent" placeholder="输入评论内容" rows="3"></textarea>
                        </div>
                        <button class="btn" onclick="addComment()">添加评论</button>
                        <div id="addCommentResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取评论列表 -->
                    <div class="api-group">
                        <h4>获取文章评论</h4>
                        <div class="form-group">
                            <label>文章ID:</label>
                            <input type="text" id="getCommentsArticleId" placeholder="输入文章ID">
                        </div>
                        <button class="btn" onclick="getComments()">获取评论列表</button>
                        <div id="commentsResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 删除评论 -->
                    <div class="api-group">
                        <h4>删除评论</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>文章ID:</label>
                                <input type="text" id="deleteCommentArticleId" placeholder="输入文章ID">
                            </div>
                            <div class="form-group">
                                <label>评论ID:</label>
                                <input type="text" id="deleteCommentId" placeholder="输入评论ID">
                            </div>
                        </div>
                        <button class="btn btn-danger" onclick="deleteComment()">删除评论</button>
                        <div id="deleteCommentResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析统计API -->
            <div class="api-section">
                <h3>📊 分析统计API</h3>
                <div class="api-content">
                    <!-- 获取统计数据 -->
                    <div class="api-group">
                        <h4>获取博客统计 (需要管理员权限)</h4>
                        <button class="btn" onclick="getStats()">获取统计数据</button>
                        <div id="statsResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取热门文章 -->
                    <div class="api-group">
                        <h4>获取热门文章</h4>
                        <div class="form-group">
                            <label>限制数量:</label>
                            <input type="number" id="popularLimit" value="10" min="1" max="50">
                        </div>
                        <button class="btn" onclick="getPopularPosts()">获取热门文章</button>
                        <div id="popularResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取标签云 -->
                    <div class="api-group">
                        <h4>获取标签云</h4>
                        <button class="btn" onclick="getTagCloud()">获取标签云</button>
                        <div id="tagCloudResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 获取发布趋势 -->
                    <div class="api-group">
                        <h4>获取发布趋势</h4>
                        <button class="btn" onclick="getPublishingTrends()">获取发布趋势</button>
                        <div id="trendsResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MongoDB高级特性展示 -->
            <div class="api-section">
                <h3>🚀 MongoDB高级特性展示</h3>
                <div class="api-content">
                    <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <h5>💡 MongoDB特性说明:</h5>
                        <p>以下功能展示了MongoDB 4.2.25的强大特性，包括聚合管道、全文搜索、复杂查询等。</p>
                    </div>

                    <!-- 聚合管道分析 -->
                    <div class="api-group">
                        <h4>🔄 聚合管道 - 用户活跃度分析</h4>
                        <p>展示MongoDB聚合管道的强大数据处理能力，包括多集合关联、字段计算、条件筛选等。</p>
                        <button class="btn" onclick="getUserAnalytics()">获取用户活跃度分析</button>
                        <div id="userAnalyticsResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 内容分析 -->
                    <div class="api-group">
                        <h4>📝 文档字段计算 - 内容分析</h4>
                        <p>展示MongoDB文档字段计算、数组操作和复杂聚合功能。</p>
                        <button class="btn" onclick="getContentAnalysis()">获取内容分析</button>
                        <div id="contentAnalysisResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 互动网络分析 -->
                    <div class="api-group">
                        <h4>🌐 多集合关联 - 用户互动网络</h4>
                        <p>展示MongoDB多集合关联查询和复杂数据关系分析。</p>
                        <button class="btn" onclick="getInteractionNetwork()">获取互动网络分析</button>
                        <div id="interactionNetworkResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 分类分布分析 -->
                    <div class="api-group">
                        <h4>📊 数据聚合 - 分类分布分析</h4>
                        <p>展示MongoDB的$lookup、$group、$sort等聚合操作。</p>
                        <button class="btn" onclick="getCategoryDistribution()">获取分类分布</button>
                        <div id="categoryDistributionResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 时间序列分析 -->
                    <div class="api-group">
                        <h4>📈 时间序列 - 发布趋势分析</h4>
                        <p>展示MongoDB日期处理和时间序列数据分析。</p>
                        <div class="form-row">
                            <div class="form-group">
                                <label>时间单位:</label>
                                <select id="timeUnit">
                                    <option value="day">按天</option>
                                    <option value="month" selected>按月</option>
                                    <option value="year">按年</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn" onclick="getPostsOverTime()">获取发布趋势</button>
                        <div id="postsOverTimeResponse" class="response-area" style="display: none;">
                            <h5>响应结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MongoDB全文搜索功能 -->
            <div class="api-section">
                <h3>🔍 MongoDB全文搜索功能</h3>
                <div class="api-content">
                    <div style="background-color: #fff3e0; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <h5>💡 全文搜索说明:</h5>
                        <p>MongoDB支持强大的全文搜索功能，包括文本索引、相关性评分、多字段搜索等。</p>
                    </div>

                    <!-- 基础全文搜索 -->
                    <div class="api-group">
                        <h4>🔎 基础全文搜索</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>搜索关键词:</label>
                                <input type="text" id="searchKeyword" placeholder="输入搜索关键词">
                            </div>
                            <div class="form-group">
                                <label>分类筛选:</label>
                                <input type="text" id="searchCategory" placeholder="分类名称（可选）">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>标签筛选:</label>
                                <input type="text" id="searchTags" placeholder="标签，用逗号分隔（可选）">
                            </div>
                            <div class="form-group">
                                <label>排序方式:</label>
                                <select id="searchSortBy">
                                    <option value="relevance">相关性</option>
                                    <option value="date">发布时间</option>
                                    <option value="views">浏览量</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn" onclick="searchArticles()">搜索文章</button>
                        <div id="searchResponse" class="response-area" style="display: none;">
                            <h5>搜索结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>

                    <!-- 高级搜索 -->
                    <div class="api-group">
                        <h4>🎯 高级搜索</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>关键词:</label>
                                <input type="text" id="advSearchKeyword" placeholder="搜索关键词">
                            </div>
                            <div class="form-group">
                                <label>作者:</label>
                                <input type="text" id="advSearchAuthor" placeholder="作者用户名">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>开始日期:</label>
                                <input type="date" id="advSearchDateFrom">
                            </div>
                            <div class="form-group">
                                <label>结束日期:</label>
                                <input type="date" id="advSearchDateTo">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>最小浏览量:</label>
                                <input type="number" id="advSearchMinViews" placeholder="0">
                            </div>
                            <div class="form-group">
                                <label>最大浏览量:</label>
                                <input type="number" id="advSearchMaxViews" placeholder="1000">
                            </div>
                        </div>
                        <button class="btn" onclick="advancedSearch()">高级搜索</button>
                        <div id="advSearchResponse" class="response-area" style="display: none;">
                            <h5>搜索结果:</h5>
                            <div class="response-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE_URL = 'http://localhost:5000/api';
        
        // 当前token
        let currentToken = '';

        // 更新token显示
        function updateTokenDisplay() {
            document.getElementById('currentToken').value = currentToken || '未登录';
        }

        // 清除token
        function clearToken() {
            currentToken = '';
            updateTokenDisplay();
            showResponse('tokenResponse', { message: 'Token已清除' }, true);
        }

        // 测试连接
        async function testConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/categories`);
                const data = await response.json();
                showResponse('connectionResponse', { 
                    status: response.status,
                    message: '服务器连接正常',
                    data: data 
                }, true);
            } catch (error) {
                showResponse('connectionResponse', { 
                    error: '无法连接到服务器',
                    details: error.message 
                }, false);
            }
        }

        // 显示响应结果
        function showResponse(elementId, data, isSuccess = true) {
            const responseDiv = document.getElementById(elementId);
            if (responseDiv) {
                responseDiv.style.display = 'block';
                responseDiv.className = `response-area ${isSuccess ? 'success' : 'error'}`;
                responseDiv.querySelector('.response-content').textContent = JSON.stringify(data, null, 2);
            }
        }

        // 发送HTTP请求的通用函数
        async function sendRequest(url, method = 'GET', body = null, requireAuth = false) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (requireAuth && currentToken) {
                options.headers['Authorization'] = `Bearer ${currentToken}`;
            }

            if (body) {
                options.body = JSON.stringify(body);
            }

            try {
                const response = await fetch(`${API_BASE_URL}${url}`, options);
                const data = await response.json();
                return { response, data };
            } catch (error) {
                throw new Error(`请求失败: ${error.message}`);
            }
        }

        // 用户注册
        async function register() {
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const role = document.getElementById('registerRole').value;

            if (!username || !email || !password) {
                alert('请填写所有必填字段');
                return;
            }

            try {
                const { response, data } = await sendRequest('/auth/register', 'POST', {
                    username, email, password, role
                });

                if (response.ok && data.token) {
                    currentToken = data.token;
                    updateTokenDisplay();
                }

                showResponse('registerResponse', data, response.ok);
            } catch (error) {
                showResponse('registerResponse', { error: error.message }, false);
            }
        }

        // 用户登录
        async function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                alert('请填写邮箱和密码');
                return;
            }

            try {
                const { response, data } = await sendRequest('/auth/login', 'POST', {
                    email, password
                });

                if (response.ok && data.token) {
                    currentToken = data.token;
                    updateTokenDisplay();
                }

                showResponse('loginResponse', data, response.ok);
            } catch (error) {
                showResponse('loginResponse', { error: error.message }, false);
            }
        }

        // 获取当前用户信息
        async function getCurrentUser() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }

            try {
                const { response, data } = await sendRequest('/auth/me', 'GET', null, true);
                showResponse('userInfoResponse', data, response.ok);
            } catch (error) {
                showResponse('userInfoResponse', { error: error.message }, false);
            }
        }

        // 创建分类
        async function createCategory() {
            const name = document.getElementById('categoryName').value;
            const description = document.getElementById('categoryDescription').value;

            if (!name) {
                alert('请输入分类名称');
                return;
            }

            try {
                const { response, data } = await sendRequest('/categories', 'POST', {
                    name, description
                }, true);

                showResponse('createCategoryResponse', data, response.ok);
            } catch (error) {
                showResponse('createCategoryResponse', { error: error.message }, false);
            }
        }

        // 获取分类列表
        async function getCategories() {
            try {
                const { response, data } = await sendRequest('/categories');
                showResponse('categoriesResponse', data, response.ok);
            } catch (error) {
                showResponse('categoriesResponse', { error: error.message }, false);
            }
        }

        // 创建文章
        async function createArticle() {
            const title = document.getElementById('articleTitle').value;
            const content = document.getElementById('articleContent').value;
            const category = document.getElementById('articleCategory').value;
            const tagsInput = document.getElementById('articleTags').value;
            const isPublished = document.getElementById('articlePublished').checked;

            if (!title || !content) {
                alert('请填写文章标题和内容');
                return;
            }

            const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()) : [];

            const articleData = {
                title,
                content,
                tags,
                isPublished
            };

            if (category) {
                articleData.category = category;
            }

            try {
                const { response, data } = await sendRequest('/articles', 'POST', articleData, true);
                showResponse('createArticleResponse', data, response.ok);
            } catch (error) {
                showResponse('createArticleResponse', { error: error.message }, false);
            }
        }

        // 获取文章列表
        async function getArticles() {
            const page = document.getElementById('articlePage').value;
            const limit = document.getElementById('articleLimit').value;
            const category = document.getElementById('articleCategoryFilter').value;
            const tags = document.getElementById('articleTagsFilter').value;

            let url = `/articles?page=${page}&limit=${limit}`;
            if (category) url += `&category=${category}`;
            if (tags) url += `&tags=${tags}`;

            try {
                const { response, data } = await sendRequest(url);
                showResponse('articlesResponse', data, response.ok);
            } catch (error) {
                showResponse('articlesResponse', { error: error.message }, false);
            }
        }

        // 获取所有文章（管理员）
        async function getAllArticles() {
            try {
                const { response, data } = await sendRequest('/articles/all', 'GET', null, true);
                showResponse('articlesResponse', data, response.ok);
            } catch (error) {
                showResponse('articlesResponse', { error: error.message }, false);
            }
        }

        // 获取单篇文章
        async function getSingleArticle() {
            const articleId = document.getElementById('singleArticleId').value;
            if (!articleId) {
                alert('请输入文章ID');
                return;
            }

            try {
                const { response, data } = await sendRequest(`/articles/${articleId}`);
                showResponse('singleArticleResponse', data, response.ok);
            } catch (error) {
                showResponse('singleArticleResponse', { error: error.message }, false);
            }
        }

        // 更新文章
        async function updateArticle() {
            const articleId = document.getElementById('updateArticleId').value;
            const title = document.getElementById('updateArticleTitle').value;
            const content = document.getElementById('updateArticleContent').value;

            if (!articleId) {
                alert('请输入文章ID');
                return;
            }

            const updateData = {};
            if (title) updateData.title = title;
            if (content) updateData.content = content;

            if (Object.keys(updateData).length === 0) {
                alert('请至少填写一个要更新的字段');
                return;
            }

            try {
                const { response, data } = await sendRequest(`/articles/${articleId}`, 'PUT', updateData, true);
                showResponse('updateArticleResponse', data, response.ok);
            } catch (error) {
                showResponse('updateArticleResponse', { error: error.message }, false);
            }
        }

        // 删除文章
        async function deleteArticle() {
            const articleId = document.getElementById('deleteArticleId').value;
            if (!articleId) {
                alert('请输入文章ID');
                return;
            }

            if (!confirm('确定要删除这篇文章吗？')) {
                return;
            }

            try {
                const { response, data } = await sendRequest(`/articles/${articleId}`, 'DELETE', null, true);
                showResponse('deleteArticleResponse', data, response.ok);
            } catch (error) {
                showResponse('deleteArticleResponse', { error: error.message }, false);
            }
        }

        // 添加评论
        async function addComment() {
            const articleId = document.getElementById('commentArticleId').value;
            const content = document.getElementById('commentContent').value;

            if (!articleId || !content) {
                alert('请填写文章ID和评论内容');
                return;
            }

            try {
                const { response, data } = await sendRequest(`/articles/${articleId}/comments`, 'POST', { content }, true);
                showResponse('addCommentResponse', data, response.ok);
            } catch (error) {
                showResponse('addCommentResponse', { error: error.message }, false);
            }
        }

        // 获取评论列表
        async function getComments() {
            const articleId = document.getElementById('getCommentsArticleId').value;
            if (!articleId) {
                alert('请输入文章ID');
                return;
            }

            try {
                const { response, data } = await sendRequest(`/articles/${articleId}/comments`);
                showResponse('commentsResponse', data, response.ok);
            } catch (error) {
                showResponse('commentsResponse', { error: error.message }, false);
            }
        }

        // 删除评论
        async function deleteComment() {
            const articleId = document.getElementById('deleteCommentArticleId').value;
            const commentId = document.getElementById('deleteCommentId').value;

            if (!articleId || !commentId) {
                alert('请填写文章ID和评论ID');
                return;
            }

            if (!confirm('确定要删除这条评论吗？')) {
                return;
            }

            try {
                const { response, data } = await sendRequest(`/articles/${articleId}/comments/${commentId}`, 'DELETE', null, true);
                showResponse('deleteCommentResponse', data, response.ok);
            } catch (error) {
                showResponse('deleteCommentResponse', { error: error.message }, false);
            }
        }

        // 获取统计数据
        async function getStats() {
            try {
                const { response, data } = await sendRequest('/analytics/stats', 'GET', null, true);
                showResponse('statsResponse', data, response.ok);
            } catch (error) {
                showResponse('statsResponse', { error: error.message }, false);
            }
        }

        // 获取热门文章
        async function getPopularPosts() {
            const limit = document.getElementById('popularLimit').value;
            const url = `/analytics/popular${limit ? `?limit=${limit}` : ''}`;

            try {
                const { response, data } = await sendRequest(url);
                showResponse('popularResponse', data, response.ok);
            } catch (error) {
                showResponse('popularResponse', { error: error.message }, false);
            }
        }

        // 获取标签云
        async function getTagCloud() {
            try {
                const { response, data } = await sendRequest('/analytics/tags');
                showResponse('tagCloudResponse', data, response.ok);
            } catch (error) {
                showResponse('tagCloudResponse', { error: error.message }, false);
            }
        }

        // 获取发布趋势
        async function getPublishingTrends() {
            try {
                const { response, data } = await sendRequest('/analytics/trends');
                showResponse('trendsResponse', data, response.ok);
            } catch (error) {
                showResponse('trendsResponse', { error: error.message }, false);
            }
        }

        // 自动测试所有API
        async function runAutoTest() {
            const testButton = document.querySelector('button[onclick="runAutoTest()"]');
            if (testButton) {
                testButton.disabled = true;
                testButton.textContent = '测试进行中...';
            }

            const testResults = [];

            try {
                // 1. 测试连接
                console.log('🔗 测试服务器连接...');
                await testConnection();
                testResults.push('✅ 服务器连接正常');

                // 2. 注册测试用户
                console.log('👤 注册测试用户...');
                document.getElementById('registerUsername').value = 'testuser_' + Date.now();
                document.getElementById('registerEmail').value = 'test_' + Date.now() + '@example.com';
                document.getElementById('registerPassword').value = 'TestPassword123!';
                await register();
                testResults.push('✅ 用户注册成功');

                // 等待一秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 3. 登录测试用户
                console.log('🔐 登录测试用户...');
                document.getElementById('loginEmail').value = document.getElementById('registerEmail').value;
                document.getElementById('loginPassword').value = 'TestPassword123!';
                await login();
                testResults.push('✅ 用户登录成功');

                // 等待一秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 4. 获取用户信息
                console.log('ℹ️ 获取用户信息...');
                await getCurrentUser();
                testResults.push('✅ 获取用户信息成功');

                // 5. 创建分类
                console.log('📁 创建测试分类...');
                document.getElementById('categoryName').value = '测试分类_' + Date.now();
                document.getElementById('categoryDescription').value = '这是一个自动测试创建的分类';
                await createCategory();
                testResults.push('✅ 创建分类成功');

                // 6. 获取分类列表
                console.log('📋 获取分类列表...');
                await getCategories();
                testResults.push('✅ 获取分类列表成功');

                // 7. 创建文章
                console.log('📝 创建测试文章...');
                document.getElementById('articleTitle').value = '自动测试文章_' + Date.now();
                document.getElementById('articleContent').value = '这是一篇由自动测试创建的文章内容。包含了各种测试数据和信息。';
                document.getElementById('articleTags').value = '自动测试,API测试,博客';
                document.getElementById('articlePublished').checked = true;
                await createArticle();
                testResults.push('✅ 创建文章成功');

                // 8. 获取文章列表
                console.log('📄 获取文章列表...');
                await getArticles();
                testResults.push('✅ 获取文章列表成功');

                // 9. 获取热门文章
                console.log('🔥 获取热门文章...');
                await getPopularPosts();
                testResults.push('✅ 获取热门文章成功');

                // 10. 获取标签云
                console.log('🏷️ 获取标签云...');
                await getTagCloud();
                testResults.push('✅ 获取标签云成功');

                console.log('🎉 自动测试完成！');
                alert('自动测试完成！\n\n' + testResults.join('\n'));

            } catch (error) {
                console.error('❌ 自动测试失败:', error);
                alert('自动测试失败: ' + error.message);
            } finally {
                if (testButton) {
                    testButton.disabled = false;
                    testButton.textContent = '🚀 运行自动测试';
                }
            }
        }

        // 清除所有响应区域
        function clearAllResponses() {
            const responseAreas = document.querySelectorAll('.response-area');
            responseAreas.forEach(area => {
                area.style.display = 'none';
                const content = area.querySelector('.response-content');
                if (content) {
                    content.textContent = '';
                }
            });
        }

        // 填充示例数据
        function fillSampleData() {
            // 注册数据
            document.getElementById('registerUsername').value = 'sampleuser';
            document.getElementById('registerEmail').value = '<EMAIL>';
            document.getElementById('registerPassword').value = 'SamplePass123!';

            // 登录数据
            document.getElementById('loginEmail').value = '<EMAIL>';
            document.getElementById('loginPassword').value = 'SamplePass123!';

            // 分类数据
            document.getElementById('categoryName').value = '技术分享';
            document.getElementById('categoryDescription').value = '分享技术相关的文章和经验';

            // 文章数据
            document.getElementById('articleTitle').value = '我的第一篇博客文章';
            document.getElementById('articleContent').value = '这是我的第一篇博客文章的内容。在这里我将分享一些有趣的技术知识和个人经验。';
            document.getElementById('articleTags').value = '技术,博客,分享';
            document.getElementById('articlePublished').checked = true;

            alert('示例数据已填充到表单中！');
        }

        // ========== MongoDB高级特性展示函数 ==========

        // 获取用户活跃度分析
        async function getUserAnalytics() {
            try {
                const { response, data } = await sendRequest('/analytics/user-analytics');
                showResponse('userAnalyticsResponse', data, response.ok);

                if (response.ok) {
                    console.log('📊 用户活跃度分析完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- 聚合管道 ($lookup, $addFields, $group)');
                    console.log('- 多集合关联查询');
                    console.log('- 复杂字段计算');
                    console.log('- 条件表达式 ($cond, $filter)');
                }
            } catch (error) {
                showResponse('userAnalyticsResponse', { error: error.message }, false);
            }
        }

        // 获取内容分析
        async function getContentAnalysis() {
            try {
                const { response, data } = await sendRequest('/analytics/content-analysis');
                showResponse('contentAnalysisResponse', data, response.ok);

                if (response.ok) {
                    console.log('📝 内容分析完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- 字符串操作 ($strLenCP, $split, $trim)');
                    console.log('- 数组操作 ($size, $ifNull)');
                    console.log('- 数学运算 ($avg, $max, $min, $round)');
                    console.log('- 数组展开 ($unwind)');
                }
            } catch (error) {
                showResponse('contentAnalysisResponse', { error: error.message }, false);
            }
        }

        // 获取互动网络分析
        async function getInteractionNetwork() {
            try {
                const { response, data } = await sendRequest('/analytics/interaction-network');
                showResponse('interactionNetworkResponse', data, response.ok);

                if (response.ok) {
                    console.log('🌐 互动网络分析完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- 多级 $lookup 关联');
                    console.log('- 复杂条件匹配 ($expr, $ne)');
                    console.log('- 数组去重 ($addToSet)');
                    console.log('- 数据分组和统计');
                }
            } catch (error) {
                showResponse('interactionNetworkResponse', { error: error.message }, false);
            }
        }

        // 获取分类分布
        async function getCategoryDistribution() {
            try {
                const { response, data } = await sendRequest('/analytics/category-distribution');
                showResponse('categoryDistributionResponse', data, response.ok);

                if (response.ok) {
                    console.log('📊 分类分布分析完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- $group 分组聚合');
                    console.log('- $lookup 集合关联');
                    console.log('- $unwind 数组展开');
                    console.log('- $project 字段投影');
                }
            } catch (error) {
                showResponse('categoryDistributionResponse', { error: error.message }, false);
            }
        }

        // 获取发布趋势
        async function getPostsOverTime() {
            try {
                const timeUnit = document.getElementById('timeUnit').value;
                const { response, data } = await sendRequest(`/analytics/posts-over-time?timeUnit=${timeUnit}`);
                showResponse('postsOverTimeResponse', data, response.ok);

                if (response.ok) {
                    console.log('📈 发布趋势分析完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- 日期格式化 ($dateToString)');
                    console.log('- 时间序列分组');
                    console.log('- 动态分组格式');
                    console.log('- 时间维度统计');
                }
            } catch (error) {
                showResponse('postsOverTimeResponse', { error: error.message }, false);
            }
        }

        // ========== MongoDB全文搜索功能 ==========

        // 基础全文搜索
        async function searchArticles() {
            try {
                const keyword = document.getElementById('searchKeyword').value;
                const category = document.getElementById('searchCategory').value;
                const tags = document.getElementById('searchTags').value;
                const sortBy = document.getElementById('searchSortBy').value;

                if (!keyword.trim()) {
                    alert('请输入搜索关键词');
                    return;
                }

                let url = `/search/articles?q=${encodeURIComponent(keyword)}&sortBy=${sortBy}`;
                if (category) url += `&category=${encodeURIComponent(category)}`;
                if (tags) url += `&tags=${encodeURIComponent(tags)}`;

                const { response, data } = await sendRequest(url);
                showResponse('searchResponse', data, response.ok);

                if (response.ok) {
                    console.log('🔍 全文搜索完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- 全文索引 ($text)');
                    console.log('- 相关性评分 ($meta: "textScore")');
                    console.log('- 多字段权重搜索');
                    console.log('- 正则表达式匹配');
                }
            } catch (error) {
                showResponse('searchResponse', { error: error.message }, false);
            }
        }

        // 高级搜索
        async function advancedSearch() {
            try {
                const keyword = document.getElementById('advSearchKeyword').value;
                const author = document.getElementById('advSearchAuthor').value;
                const dateFrom = document.getElementById('advSearchDateFrom').value;
                const dateTo = document.getElementById('advSearchDateTo').value;
                const minViews = document.getElementById('advSearchMinViews').value;
                const maxViews = document.getElementById('advSearchMaxViews').value;

                let url = '/search/advanced?';
                const params = [];

                if (keyword) params.push(`keyword=${encodeURIComponent(keyword)}`);
                if (author) params.push(`author=${encodeURIComponent(author)}`);
                if (dateFrom) params.push(`dateFrom=${dateFrom}`);
                if (dateTo) params.push(`dateTo=${dateTo}`);
                if (minViews) params.push(`minViews=${minViews}`);
                if (maxViews) params.push(`maxViews=${maxViews}`);

                url += params.join('&');

                const { response, data } = await sendRequest(url);
                showResponse('advSearchResponse', data, response.ok);

                if (response.ok) {
                    console.log('🎯 高级搜索完成');
                    console.log('展示的MongoDB特性：');
                    console.log('- 复杂查询条件组合');
                    console.log('- 日期范围查询');
                    console.log('- 数值范围查询');
                    console.log('- 多集合关联搜索');
                }
            } catch (error) {
                showResponse('advSearchResponse', { error: error.message }, false);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTokenDisplay();

            // 自动测试连接
            setTimeout(() => {
                testConnection();
            }, 1000);
        });
    </script>
</body>
</html>
