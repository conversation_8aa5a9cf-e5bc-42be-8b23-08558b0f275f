/**
 * MongoDB博客平台增强工具
 * 
 * 该脚本自动应用MongoDB优化和增强功能到博客平台
 * 充分利用MongoDB 4.2.25版本的特性
 */

const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { program } = require('commander');
const colors = require('colors');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 增强模块
const modelEnhancer = require('./enhancers/model-enhancer');
const indexOptimizer = require('./enhancers/index-optimizer');
const aggregationSetup = require('./enhancers/aggregation-setup');
const performanceOptimizer = require('./enhancers/performance-optimizer');

// 配置CLI
program
  .version('1.0.0')
  .description('MongoDB博客平台增强工具')
  .option('-a, --all', '应用所有增强功能')
  .option('-m, --models', '仅增强数据模型')
  .option('-i, --indexes', '仅优化索引')
  .option('-g, --aggregation', '仅设置聚合管道')
  .option('-p, --performance', '仅应用性能优化')
  .option('--stage <number>', '执行特定阶段 (1-3)', parseInt)
  .option('--dry-run', '显示将执行的更改但不应用')
  .option('--config <path>', '指定自定义配置文件路径')
  .parse(process.argv);

const options = program.opts();

// 默认配置
const defaultConfig = {
  // 数据库配置在环境变量中或这里指定默认值
  dbUri: process.env.MONGO_URI || 'mongodb://localhost:27017/modern-blog',
  
  // 模型增强配置
  modelEnhancements: {
    enableContentBlocks: true,
    enableNestedComments: true,
    enableVersioning: true,
    enableMetadata: true
  },
  
  // 索引优化配置
  indexOptimizations: {
    enableTextSearch: true,
    enableCompoundIndexes: true,
    enableExpiryIndexes: true
  },
  
  // 聚合管道配置
  aggregationSetup: {
    enableDashboard: true,
    enableContentAnalytics: true,
    enableUserAnalytics: true
  },
  
  // 性能优化配置
  performanceOptimizations: {
    enableCaching: true,
    enableCompression: true,
    enableBulkOperations: true
  }
};

// 加载自定义配置
let config = { ...defaultConfig };
if (options.config) {
  try {
    const customConfig = require(path.resolve(options.config));
    config = { ...config, ...customConfig };
    console.log(colors.green('✓ 已加载自定义配置'));
  } catch (err) {
    console.error(colors.red('✗ 加载自定义配置失败:'), err.message);
    process.exit(1);
  }
}

// 连接数据库
async function connectDB() {
  try {
    console.log(colors.cyan('正在连接到MongoDB...'));
    const conn = await mongoose.connect(config.dbUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      // 4.2.25版本兼容选项
    });
    
    console.log(colors.green(`✓ MongoDB已连接: ${conn.connection.host}`));
    return conn;
  } catch (error) {
    console.error(colors.red('✗ 数据库连接失败:'), error.message);
    process.exit(1);
  }
}

// 执行特定阶段
async function runStage(stage) {
  console.log(colors.cyan(`\n[执行阶段 ${stage}]`));
  
  switch(stage) {
    case 1:
      // 阶段1: 文档模型增强与基础优化
      if (options.all || options.models) {
        await modelEnhancer.enhance(config.modelEnhancements, options.dryRun);
      }
      if (options.all || options.indexes) {
        await indexOptimizer.optimize(config.indexOptimizations, options.dryRun);
      }
      break;
      
    case 2:
      // 阶段2: 高级查询与分析功能
      if (options.all || options.aggregation) {
        await aggregationSetup.setup(config.aggregationSetup, options.dryRun);
      }
      break;
      
    case 3:
      // 阶段3: 性能与扩展性优化
      if (options.all || options.performance) {
        await performanceOptimizer.optimize(config.performanceOptimizations, options.dryRun);
      }
      break;
      
    default:
      console.log(colors.yellow('无效的阶段编号，跳过...'));
  }
}

// 执行所有增强
async function enhanceMongoDB() {
  try {
    console.log(colors.cyan('=== MongoDB博客平台增强工具 ==='));
    
    // 如果是演示模式，显示提示
    if (options.dryRun) {
      console.log(colors.yellow('⚠ 演示模式: 将显示但不应用更改'));
    }
    
    // 连接数据库
    const conn = await connectDB();
    
    // 检查指定的阶段或执行所有阶段
    if (options.stage) {
      await runStage(options.stage);
    } else {
      // 执行所有阶段
      await runStage(1);
      await runStage(2);
      await runStage(3);
    }
    
    console.log(colors.green('\n✓ MongoDB增强完成!'));
    
    // 关闭数据库连接
    await mongoose.disconnect();
    console.log(colors.cyan('数据库连接已关闭'));
    
  } catch (error) {
    console.error(colors.red('\n✗ 增强过程中发生错误:'));
    console.error(error);
    process.exit(1);
  }
}

// 执行主函数
enhanceMongoDB(); 