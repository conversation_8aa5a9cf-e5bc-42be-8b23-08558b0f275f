import { CONFIG, API_ENDPOINTS, ERROR_MESSAGES } from './config.js';
import { Utils } from './utils.js';

// HTTP客户端类
class HttpClient {
    constructor(baseURL = CONFIG.API.BASE_URL) {
        this.baseURL = baseURL;
        this.timeout = CONFIG.API.TIMEOUT;
        this.retryAttempts = CONFIG.API.RETRY_ATTEMPTS;
        this.retryDelay = CONFIG.API.RETRY_DELAY;
    }
    
    // 获取认证头
    getAuthHeaders() {
        const token = Utils.storage.get(CONFIG.STORAGE_KEYS.TOKEN);
        return token ? { 'Authorization': `Bearer ${token}` } : {};
    }
    
    // 构建完整URL
    buildUrl(endpoint, params = {}) {
        let url = this.baseURL + endpoint;
        
        // 替换路径参数
        Object.keys(params).forEach(key => {
            if (url.includes(`:${key}`)) {
                url = url.replace(`:${key}`, params[key]);
                delete params[key];
            }
        });
        
        // 添加查询参数
        const queryString = Utils.buildUrlParams(params);
        if (queryString) {
            url += `?${queryString}`;
        }
        
        return url;
    }
    
    // 处理响应
    async handleResponse(response) {
        if (!response.ok) {
            const error = await this.handleError(response);
            throw error;
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        
        return await response.text();
    }
    
    // 处理错误
    async handleError(response) {
        let errorMessage = ERROR_MESSAGES.UNKNOWN;
        let errorData = null;
        
        try {
            errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
        } catch (e) {
            // 如果无法解析JSON，使用状态码对应的错误消息
            switch (response.status) {
                case 401:
                    errorMessage = ERROR_MESSAGES.UNAUTHORIZED;
                    break;
                case 403:
                    errorMessage = ERROR_MESSAGES.FORBIDDEN;
                    break;
                case 404:
                    errorMessage = ERROR_MESSAGES.NOT_FOUND;
                    break;
                case 500:
                    errorMessage = ERROR_MESSAGES.SERVER_ERROR;
                    break;
                default:
                    errorMessage = ERROR_MESSAGES.UNKNOWN;
            }
        }
        
        return {
            status: response.status,
            message: errorMessage,
            data: errorData
        };
    }
    
    // 重试机制
    async withRetry(requestFn, attempts = this.retryAttempts) {
        try {
            return await requestFn();
        } catch (error) {
            if (attempts > 1 && error.status >= 500) {
                await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                return this.withRetry(requestFn, attempts - 1);
            }
            throw error;
        }
    }
    
    // GET请求
    async get(endpoint, params = {}) {
        return this.withRetry(async () => {
            const url = this.buildUrl(endpoint, params);
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders()
                },
                signal: AbortSignal.timeout(this.timeout)
            });
            
            return this.handleResponse(response);
        });
    }
    
    // POST请求
    async post(endpoint, data = {}, params = {}) {
        return this.withRetry(async () => {
            const url = this.buildUrl(endpoint, params);
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders()
                },
                body: JSON.stringify(data),
                signal: AbortSignal.timeout(this.timeout)
            });
            
            return this.handleResponse(response);
        });
    }
    
    // PUT请求
    async put(endpoint, data = {}, params = {}) {
        return this.withRetry(async () => {
            const url = this.buildUrl(endpoint, params);
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders()
                },
                body: JSON.stringify(data),
                signal: AbortSignal.timeout(this.timeout)
            });
            
            return this.handleResponse(response);
        });
    }
    
    // DELETE请求
    async delete(endpoint, params = {}) {
        return this.withRetry(async () => {
            const url = this.buildUrl(endpoint, params);
            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders()
                },
                signal: AbortSignal.timeout(this.timeout)
            });
            
            return this.handleResponse(response);
        });
    }
}

// 博客API类
export class BlogAPI {
    constructor() {
        this.http = new HttpClient();
    }
    
    // ========== 认证相关 ==========
    
    // 用户注册
    async register(userData) {
        return this.http.post(API_ENDPOINTS.AUTH.REGISTER, userData);
    }
    
    // 用户登录
    async login(credentials) {
        const response = await this.http.post(API_ENDPOINTS.AUTH.LOGIN, credentials);
        
        // 保存token和用户信息
        if (response.token) {
            Utils.storage.set(CONFIG.STORAGE_KEYS.TOKEN, response.token);
            Utils.storage.set(CONFIG.STORAGE_KEYS.USER, response.user);
        }
        
        return response;
    }
    
    // 获取当前用户信息
    async getMe() {
        return this.http.get(API_ENDPOINTS.AUTH.ME);
    }
    
    // 用户登出
    logout() {
        Utils.storage.remove(CONFIG.STORAGE_KEYS.TOKEN);
        Utils.storage.remove(CONFIG.STORAGE_KEYS.USER);
    }
    
    // ========== 文章相关 ==========
    
    // 获取文章列表（公开）
    async getArticles(params = {}) {
        return this.http.get(API_ENDPOINTS.ARTICLES.LIST, params);
    }
    
    // 获取所有文章（管理员）
    async getAllArticles(params = {}) {
        return this.http.get(API_ENDPOINTS.ARTICLES.ALL, params);
    }
    
    // 获取单篇文章
    async getArticle(id) {
        return this.http.get(API_ENDPOINTS.ARTICLES.DETAIL, { id });
    }
    
    // 创建文章
    async createArticle(articleData) {
        return this.http.post(API_ENDPOINTS.ARTICLES.CREATE, articleData);
    }
    
    // 更新文章
    async updateArticle(id, articleData) {
        return this.http.put(API_ENDPOINTS.ARTICLES.UPDATE, articleData, { id });
    }
    
    // 删除文章
    async deleteArticle(id) {
        return this.http.delete(API_ENDPOINTS.ARTICLES.DELETE, { id });
    }
    
    // ========== 评论相关 ==========
    
    // 获取文章评论
    async getComments(articleId, params = {}) {
        return this.http.get(API_ENDPOINTS.COMMENTS.LIST, { articleId, ...params });
    }
    
    // 添加评论
    async addComment(articleId, commentData) {
        return this.http.post(API_ENDPOINTS.COMMENTS.CREATE, commentData, { articleId });
    }
    
    // 删除评论
    async deleteComment(articleId, commentId) {
        return this.http.delete(API_ENDPOINTS.COMMENTS.DELETE, { articleId, id: commentId });
    }
    
    // ========== 分类相关 ==========
    
    // 获取分类列表
    async getCategories() {
        return this.http.get(API_ENDPOINTS.CATEGORIES.LIST);
    }
    
    // 获取单个分类
    async getCategory(id) {
        return this.http.get(API_ENDPOINTS.CATEGORIES.DETAIL, { id });
    }
    
    // 创建分类
    async createCategory(categoryData) {
        return this.http.post(API_ENDPOINTS.CATEGORIES.CREATE, categoryData);
    }
    
    // 更新分类
    async updateCategory(id, categoryData) {
        return this.http.put(API_ENDPOINTS.CATEGORIES.UPDATE, categoryData, { id });
    }
    
    // 删除分类
    async deleteCategory(id) {
        return this.http.delete(API_ENDPOINTS.CATEGORIES.DELETE, { id });
    }
    
    // ========== 搜索相关 ==========
    
    // 搜索文章
    async searchArticles(query, params = {}) {
        return this.http.get(API_ENDPOINTS.SEARCH.ARTICLES, { q: query, ...params });
    }
    
    // 高级搜索
    async advancedSearch(searchParams) {
        return this.http.get(API_ENDPOINTS.SEARCH.ADVANCED, searchParams);
    }

    // ========== 分析相关 ==========

    // 获取基础统计
    async getStats() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.STATS);
    }

    // 获取热门文章
    async getPopularPosts() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.POPULAR);
    }

    // 获取标签云
    async getTagCloud() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.TAGS);
    }

    // 获取分类分布
    async getCategoryDistribution() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.CATEGORY_DISTRIBUTION);
    }

    // 获取发文趋势
    async getPostsOverTime() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.POSTS_OVER_TIME);
    }

    // 获取用户分析
    async getUserAnalytics() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.USER_ANALYTICS);
    }

    // 获取内容分析
    async getContentAnalysis() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.CONTENT_ANALYSIS);
    }

    // 获取交互网络
    async getInteractionNetwork() {
        return this.http.get(API_ENDPOINTS.ANALYTICS.INTERACTION_NETWORK);
    }
}

// 创建API实例
export const api = new BlogAPI();

// 导出默认API实例
export default api;
