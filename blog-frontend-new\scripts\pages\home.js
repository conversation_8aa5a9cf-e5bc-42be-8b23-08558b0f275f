import { api } from '../api.js';
import { appState } from '../state.js';
import { router } from '../router.js';
import { CONFIG } from '../config.js';
import { Utils } from '../utils.js';

// 首页组件
export class HomePage {
    constructor() {
        this.currentPage = 1;
        this.totalPages = 1;
        this.articles = [];
        this.categories = [];
        this.popularArticles = [];
        this.tags = [];
    }
    
    // 渲染首页
    async render(params = {}, query = {}) {
        try {
            // 显示加载状态
            appState.setLoading(true, '加载首页内容...');
            
            // 获取查询参数
            this.currentPage = parseInt(query.page) || 1;
            const category = query.category;
            const tag = query.tag;
            
            // 并行加载数据
            await Promise.all([
                this.loadArticles(this.currentPage, category, tag),
                this.loadSidebarData()
            ]);
            
            // 渲染页面内容
            this.renderContent();
            
            // 设置事件监听器
            this.setupEventListeners();
            
        } catch (error) {
            console.error('Home page render error:', error);
            this.renderError(error);
        } finally {
            appState.setLoading(false);
        }
    }
    
    // 加载文章列表
    async loadArticles(page = 1, category = null, tag = null) {
        try {
            const params = {
                page,
                limit: CONFIG.PAGINATION.DEFAULT_PAGE_SIZE
            };
            
            if (category) params.category = category;
            if (tag) params.tag = tag;
            
            const response = await api.getArticles(params);
            
            this.articles = response.articles || response.data || [];
            this.currentPage = response.currentPage || page;
            this.totalPages = response.totalPages || Math.ceil((response.total || 0) / CONFIG.PAGINATION.DEFAULT_PAGE_SIZE);
            
        } catch (error) {
            console.error('Failed to load articles:', error);
            throw error;
        }
    }
    
    // 加载侧边栏数据
    async loadSidebarData() {
        try {
            const [categories, popularArticles, tags] = await Promise.all([
                api.getCategories(),
                api.getPopularPosts(),
                api.getTags()
            ]);
            
            this.categories = categories || [];
            this.popularArticles = popularArticles || [];
            this.tags = tags || [];
            
        } catch (error) {
            console.error('Failed to load sidebar data:', error);
            // 侧边栏数据加载失败不影响主要内容
        }
    }
    
    // 渲染页面内容
    renderContent() {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;
        
        mainContent.innerHTML = `
            <div class="home-page">
                <div class="container">
                    <div class="row">
                        <!-- 主要内容区域 -->
                        <div class="col-lg-8 col-md-12">
                            <div class="main-content">
                                ${this.renderArticleList()}
                                ${this.renderPagination()}
                            </div>
                        </div>
                        
                        <!-- 侧边栏 -->
                        <div class="col-lg-4 col-md-12">
                            <div class="sidebar">
                                ${this.renderSidebar()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 渲染文章列表
    renderArticleList() {
        if (!this.articles || this.articles.length === 0) {
            return `
                <div class="no-articles">
                    <div class="no-content">
                        <i class="fas fa-file-alt"></i>
                        <h3>暂无文章</h3>
                        <p>还没有发布任何文章，请稍后再来查看。</p>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="article-list">
                ${this.articles.map(article => this.renderArticleCard(article)).join('')}
            </div>
        `;
    }
    
    // 渲染文章卡片
    renderArticleCard(article) {
        const excerpt = article.excerpt || article.content?.substring(0, 200) + '...' || '';
        const imageUrl = article.featuredImage || '/images/default-article.jpg';
        const categoryName = article.category?.name || '未分类';
        const authorName = article.author?.name || '匿名';
        
        return `
            <article class="article-card" data-id="${article._id}">
                <div class="article-image">
                    <img src="${imageUrl}" alt="${article.title}" loading="lazy">
                    <div class="article-category">
                        <span class="category-tag">${categoryName}</span>
                    </div>
                </div>
                
                <div class="article-content">
                    <h2 class="article-title">
                        <a href="/article/${article._id}" data-route="/article/${article._id}">
                            ${article.title}
                        </a>
                    </h2>
                    
                    <div class="article-meta">
                        <span class="author">
                            <i class="fas fa-user"></i>
                            ${authorName}
                        </span>
                        <span class="date">
                            <i class="fas fa-calendar"></i>
                            ${Utils.formatDate(article.createdAt, 'RELATIVE')}
                        </span>
                        <span class="views">
                            <i class="fas fa-eye"></i>
                            ${Utils.formatNumber(article.views || 0)}
                        </span>
                        <span class="comments">
                            <i class="fas fa-comments"></i>
                            ${Utils.formatNumber(article.commentCount || 0)}
                        </span>
                    </div>
                    
                    <div class="article-excerpt">
                        ${excerpt}
                    </div>
                    
                    <div class="article-tags">
                        ${(article.tags || []).map(tag => 
                            `<span class="tag" data-tag="${tag}">#${tag}</span>`
                        ).join('')}
                    </div>
                    
                    <div class="article-actions">
                        <a href="/article/${article._id}" class="read-more" data-route="/article/${article._id}">
                            阅读全文 <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </article>
        `;
    }
    
    // 渲染分页
    renderPagination() {
        if (this.totalPages <= 1) return '';
        
        const pagination = [];
        const maxVisible = 5;
        const startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        const endPage = Math.min(this.totalPages, startPage + maxVisible - 1);
        
        // 上一页
        if (this.currentPage > 1) {
            pagination.push(`
                <button class="page-btn prev-btn" data-page="${this.currentPage - 1}">
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>
            `);
        }
        
        // 页码
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === this.currentPage ? 'active' : '';
            pagination.push(`
                <button class="page-btn ${isActive}" data-page="${i}">
                    ${i}
                </button>
            `);
        }
        
        // 下一页
        if (this.currentPage < this.totalPages) {
            pagination.push(`
                <button class="page-btn next-btn" data-page="${this.currentPage + 1}">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>
            `);
        }
        
        return `
            <div class="pagination">
                <div class="pagination-info">
                    第 ${this.currentPage} 页，共 ${this.totalPages} 页
                </div>
                <div class="pagination-buttons">
                    ${pagination.join('')}
                </div>
            </div>
        `;
    }
    
    // 渲染侧边栏
    renderSidebar() {
        return `
            ${this.renderSearchBox()}
            ${this.renderCategories()}
            ${this.renderPopularArticles()}
            ${this.renderTagCloud()}
        `;
    }
    
    // 渲染搜索框
    renderSearchBox() {
        return `
            <div class="sidebar-widget search-widget">
                <h3 class="widget-title">搜索文章</h3>
                <form class="search-form" id="sidebarSearchForm">
                    <div class="search-input-group">
                        <input type="text" id="sidebarSearch" placeholder="输入关键词搜索..." required>
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        `;
    }
    
    // 渲染分类列表
    renderCategories() {
        if (!this.categories || this.categories.length === 0) return '';
        
        return `
            <div class="sidebar-widget categories-widget">
                <h3 class="widget-title">文章分类</h3>
                <ul class="category-list">
                    ${this.categories.map(category => `
                        <li class="category-item">
                            <a href="/?category=${category._id}" class="category-link" data-route="/?category=${category._id}">
                                <span class="category-name">${category.name}</span>
                                <span class="category-count">(${category.articleCount || 0})</span>
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    }
    
    // 渲染热门文章
    renderPopularArticles() {
        if (!this.popularArticles || this.popularArticles.length === 0) return '';
        
        return `
            <div class="sidebar-widget popular-widget">
                <h3 class="widget-title">热门文章</h3>
                <ul class="popular-list">
                    ${this.popularArticles.slice(0, 5).map((article, index) => `
                        <li class="popular-item">
                            <div class="popular-rank">${index + 1}</div>
                            <div class="popular-content">
                                <a href="/article/${article._id}" class="popular-title" data-route="/article/${article._id}">
                                    ${article.title}
                                </a>
                                <div class="popular-meta">
                                    <span class="popular-views">
                                        <i class="fas fa-eye"></i>
                                        ${Utils.formatNumber(article.views || 0)}
                                    </span>
                                </div>
                            </div>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    }
    
    // 渲染标签云
    renderTagCloud() {
        if (!this.tags || this.tags.length === 0) return '';
        
        return `
            <div class="sidebar-widget tags-widget">
                <h3 class="widget-title">热门标签</h3>
                <div class="tag-cloud">
                    ${this.tags.slice(0, 20).map(tag => `
                        <a href="/?tag=${tag.name}" class="tag-item" data-route="/?tag=${tag.name}">
                            #${tag.name}
                        </a>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 分页按钮点击
        document.addEventListener('click', this.handlePagination.bind(this));
        
        // 侧边栏搜索
        const searchForm = document.getElementById('sidebarSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSidebarSearch.bind(this));
        }
        
        // 标签点击
        document.addEventListener('click', this.handleTagClick.bind(this));
    }
    
    // 处理分页
    handlePagination(event) {
        const pageBtn = event.target.closest('.page-btn');
        if (!pageBtn) return;
        
        const page = parseInt(pageBtn.dataset.page);
        if (page && page !== this.currentPage) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('page', page);
            router.navigate(currentUrl.pathname + currentUrl.search);
        }
    }
    
    // 处理侧边栏搜索
    handleSidebarSearch(event) {
        event.preventDefault();
        const searchInput = document.getElementById('sidebarSearch');
        const query = searchInput.value.trim();
        
        if (query) {
            router.navigate(`/search?q=${encodeURIComponent(query)}`);
        }
    }
    
    // 处理标签点击
    handleTagClick(event) {
        const tagElement = event.target.closest('.tag');
        if (!tagElement) return;
        
        event.preventDefault();
        const tag = tagElement.dataset.tag;
        if (tag) {
            router.navigate(`/?tag=${encodeURIComponent(tag)}`);
        }
    }
    
    // 渲染错误页面
    renderError(error) {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;
        
        mainContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>加载失败</h2>
                    <p>${error.message || '页面加载失败，请稍后重试'}</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
            </div>
        `;
    }
}

// 创建首页实例
const homePage = new HomePage();

// 导出渲染函数
export const render = (params, query) => homePage.render(params, query);

// 导出默认实例
export default homePage;
