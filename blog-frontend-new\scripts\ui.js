import { appState } from './state.js';
import { CONFIG } from './config.js';
import { Utils } from './utils.js';

// UI管理类
export class UIManager {
    constructor() {
        this.initEventListeners();
        this.initComponents();
    }
    
    // 初始化事件监听器
    initEventListeners() {
        // 导航栏切换
        document.addEventListener('click', this.handleNavToggle.bind(this));
        
        // 搜索功能
        document.addEventListener('input', this.handleSearch.bind(this));
        document.addEventListener('submit', this.handleSearchSubmit.bind(this));
        
        // 用户菜单
        document.addEventListener('click', this.handleUserMenu.bind(this));
        
        // 响应式处理
        window.addEventListener('resize', Utils.throttle(this.handleResize.bind(this), 100));
        
        // 监听状态变化
        appState.subscribe(this.handleStateChange.bind(this));
    }
    
    // 初始化组件
    initComponents() {
        this.initToastContainer();
        this.initLoadingOverlay();
        this.initScrollToTop();
        this.handleResize();
    }
    
    // 处理导航栏切换
    handleNavToggle(event) {
        const target = event.target;
        
        // 移动端导航栏切换
        if (target.id === 'navToggle' || target.closest('#navToggle')) {
            this.toggleMobileNav();
        }
        
        // 管理后台侧边栏切换
        else if (target.id === 'sidebarToggle' || target.closest('#sidebarToggle')) {
            this.toggleSidebar();
        }
        
        // 移动端管理后台侧边栏切换
        else if (target.id === 'mobileSidebarToggle' || target.closest('#mobileSidebarToggle')) {
            this.toggleMobileSidebar();
        }
    }
    
    // 切换移动端导航栏
    toggleMobileNav() {
        const navMenu = document.getElementById('navMenu');
        const navToggle = document.getElementById('navToggle');
        
        if (navMenu && navToggle) {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        }
    }
    
    // 切换侧边栏
    toggleSidebar() {
        const sidebar = document.getElementById('adminSidebar');
        const body = document.body;
        
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
            body.classList.toggle('sidebar-collapsed');
        }
    }
    
    // 切换移动端侧边栏
    toggleMobileSidebar() {
        const sidebar = document.getElementById('adminSidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.toggle('mobile-open');
            
            // 创建或移除遮罩层
            if (sidebar.classList.contains('mobile-open')) {
                if (!overlay) {
                    const newOverlay = document.createElement('div');
                    newOverlay.className = 'sidebar-overlay';
                    newOverlay.addEventListener('click', () => {
                        this.toggleMobileSidebar();
                    });
                    document.body.appendChild(newOverlay);
                }
            } else {
                if (overlay) {
                    overlay.remove();
                }
            }
        }
    }
    
    // 处理搜索输入
    handleSearch(event) {
        const target = event.target;
        
        if (target.id === 'globalSearch') {
            this.debounceSearch(target.value);
        }
    }
    
    // 防抖搜索
    debounceSearch = Utils.debounce((query) => {
        if (query.length >= CONFIG.SEARCH.MIN_QUERY_LENGTH) {
            this.performSearch(query);
        } else {
            this.clearSearchResults();
        }
    }, CONFIG.UI.DEBOUNCE_DELAY);
    
    // 执行搜索
    async performSearch(query) {
        try {
            // 这里可以实现实时搜索建议
            console.log('Searching for:', query);
        } catch (error) {
            console.error('Search error:', error);
        }
    }
    
    // 清除搜索结果
    clearSearchResults() {
        // 清除搜索建议
    }
    
    // 处理搜索表单提交
    handleSearchSubmit(event) {
        const form = event.target;
        if (form.querySelector('#globalSearch')) {
            event.preventDefault();
            const query = form.querySelector('#globalSearch').value.trim();
            if (query) {
                // 导航到搜索页面
                window.location.href = `/search?q=${encodeURIComponent(query)}`;
            }
        }
    }
    
    // 处理用户菜单
    handleUserMenu(event) {
        const target = event.target;
        
        // 用户头像点击
        if (target.id === 'userAvatar' || target.closest('#userAvatar')) {
            this.toggleUserDropdown();
        }
        
        // 管理后台用户头像点击
        else if (target.id === 'headerUserAvatar' || target.closest('#headerUserAvatar')) {
            this.toggleAdminUserDropdown();
        }
        
        // 点击其他地方关闭下拉菜单
        else if (!target.closest('.user-menu') && !target.closest('.admin-user-menu')) {
            this.closeAllDropdowns();
        }
    }
    
    // 切换用户下拉菜单
    toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
            dropdown.classList.toggle('active');
        }
    }
    
    // 切换管理后台用户下拉菜单
    toggleAdminUserDropdown() {
        const userMenu = document.querySelector('.admin-user-menu');
        if (userMenu) {
            userMenu.classList.toggle('active');
        }
    }
    
    // 关闭所有下拉菜单
    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('.user-dropdown, .admin-user-menu');
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    }
    
    // 处理窗口大小变化
    handleResize() {
        const width = window.innerWidth;
        
        // 移动端处理
        if (width <= 768) {
            this.handleMobileLayout();
        } else {
            this.handleDesktopLayout();
        }
    }
    
    // 移动端布局处理
    handleMobileLayout() {
        // 关闭导航菜单
        const navMenu = document.getElementById('navMenu');
        const navToggle = document.getElementById('navToggle');
        
        if (navMenu && navToggle) {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        }
        
        // 关闭管理后台侧边栏
        const sidebar = document.getElementById('adminSidebar');
        if (sidebar) {
            sidebar.classList.remove('mobile-open');
        }
        
        // 移除侧边栏遮罩
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
    
    // 桌面端布局处理
    handleDesktopLayout() {
        // 桌面端特定处理
    }
    
    // 初始化Toast容器
    initToastContainer() {
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
    }
    
    // 显示Toast通知
    showToast(toast) {
        const container = document.getElementById('toastContainer');
        if (!container) return;
        
        const toastElement = document.createElement('div');
        toastElement.className = `toast toast-${toast.type}`;
        toastElement.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">
                    <i class="fas fa-${this.getToastIcon(toast.type)}"></i>
                </div>
                <div class="toast-message">${toast.message}</div>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        container.appendChild(toastElement);
        
        // 添加显示动画
        setTimeout(() => {
            toastElement.classList.add('show');
        }, 10);
        
        // 自动移除
        if (toast.duration > 0) {
            setTimeout(() => {
                this.removeToast(toastElement);
            }, toast.duration);
        }
    }
    
    // 移除Toast
    removeToast(toastElement) {
        if (toastElement && toastElement.parentElement) {
            toastElement.classList.add('hide');
            setTimeout(() => {
                if (toastElement.parentElement) {
                    toastElement.remove();
                }
            }, 300);
        }
    }
    
    // 获取Toast图标
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    // 初始化加载遮罩
    initLoadingOverlay() {
        let overlay = document.getElementById('loadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.className = 'loading-overlay hidden';
            overlay.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>
            `;
            document.body.appendChild(overlay);
        }
    }
    
    // 显示加载状态
    showLoading(message = '加载中...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            const messageElement = overlay.querySelector('p');
            if (messageElement) {
                messageElement.textContent = message;
            }
            overlay.classList.remove('hidden');
        }
    }
    
    // 隐藏加载状态
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }
    
    // 初始化回到顶部按钮
    initScrollToTop() {
        let button = document.getElementById('scrollToTop');
        if (!button) {
            button = document.createElement('button');
            button.id = 'scrollToTop';
            button.className = 'scroll-to-top hidden';
            button.innerHTML = '<i class="fas fa-arrow-up"></i>';
            button.addEventListener('click', this.scrollToTop);
            document.body.appendChild(button);
        }
        
        // 监听滚动事件
        window.addEventListener('scroll', Utils.throttle(() => {
            if (window.pageYOffset > 300) {
                button.classList.remove('hidden');
            } else {
                button.classList.add('hidden');
            }
        }, 100));
    }
    
    // 滚动到顶部
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }
    
    // 处理状态变化
    handleStateChange(currentState, prevState) {
        // 加载状态变化
        if (currentState.loading !== prevState.loading) {
            if (currentState.loading) {
                this.showLoading(currentState.loadingMessage);
            } else {
                this.hideLoading();
            }
        }
        
        // Toast通知变化
        if (currentState.toasts.length > prevState.toasts.length) {
            const newToasts = currentState.toasts.slice(prevState.toasts.length);
            newToasts.forEach(toast => {
                this.showToast(toast);
            });
        }
        
        // 侧边栏状态变化
        if (currentState.sidebarOpen !== prevState.sidebarOpen) {
            if (currentState.sidebarOpen) {
                this.toggleSidebar();
            }
        }
    }
    
    // 显示确认对话框
    showConfirm(title, message, onConfirm, onCancel) {
        const modal = document.getElementById('confirmModal');
        if (!modal) return;
        
        const titleElement = modal.querySelector('#confirmTitle');
        const messageElement = modal.querySelector('#confirmMessage');
        const confirmBtn = modal.querySelector('#confirmOk');
        const cancelBtn = modal.querySelector('#confirmCancel');
        
        if (titleElement) titleElement.textContent = title;
        if (messageElement) messageElement.textContent = message;
        
        // 绑定事件
        const handleConfirm = () => {
            modal.classList.add('hidden');
            if (onConfirm) onConfirm();
            cleanup();
        };
        
        const handleCancel = () => {
            modal.classList.add('hidden');
            if (onCancel) onCancel();
            cleanup();
        };
        
        const cleanup = () => {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
        };
        
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        
        modal.classList.remove('hidden');
    }
    
    // 格式化数字显示
    formatNumber(num) {
        return Utils.formatNumber(num);
    }
    
    // 格式化日期显示
    formatDate(date, format = 'RELATIVE') {
        return Utils.formatDate(date, format);
    }
}

// 创建全局UI管理实例
export const uiManager = new UIManager();

// 导出默认UI管理实例
export default uiManager;
