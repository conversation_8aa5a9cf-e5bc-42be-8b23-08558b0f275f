// ========== 主应用类 ==========
class BlogApp {
    constructor() {
        this.currentPage = 1;
        this.currentCategory = '';
        this.currentSort = 'date';
        this.articles = [];
        this.categories = [];
        this.stats = {};
        this.init();
    }
    
    // 初始化应用
    async init() {
        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                this.start();
            }
        } catch (error) {
            console.error('App initialization error:', error);
            UI.showToast('应用初始化失败', 'error');
        }
    }
    
    // 启动应用
    async start() {
        try {
            console.log('启动博客应用...');
            UI.showLoading();

            // 测试API连接
            console.log('测试API连接...');

            // 并行加载初始数据
            console.log('加载初始数据...');
            const results = await Promise.allSettled([
                this.loadArticles(),
                this.loadCategories(),
                this.loadStats()
            ]);

            // 检查加载结果
            results.forEach((result, index) => {
                const names = ['文章', '分类', '统计'];
                if (result.status === 'rejected') {
                    console.error(`加载${names[index]}失败:`, result.reason);
                } else {
                    console.log(`加载${names[index]}成功`);
                }
            });

            // 渲染页面内容
            console.log('渲染页面内容...');
            this.renderFeaturedArticles();
            this.renderArticles();
            this.renderCategories();
            this.renderStats();
            this.renderSidebar();

            // 绑定事件
            this.bindEvents();

            UI.hideLoading();
            console.log('博客应用启动完成');

            // 处理URL参数
            this.handleUrlParams();

        } catch (error) {
            UI.hideLoading();
            console.error('App start error:', error);
            UI.showToast('加载数据失败，请刷新页面重试', 'error');

            // 显示离线模式
            this.showOfflineMode();
        }
    }
    
    // ========== 数据加载 ==========
    async loadArticles(params = {}) {
        try {
            const defaultParams = {
                page: this.currentPage,
                limit: CONFIG.PAGINATION.DEFAULT_PAGE_SIZE,
                category: this.currentCategory,
                sort: this.currentSort,
                ...params
            };
            
            const response = await API.getArticles(defaultParams);
            
            if (response.success) {
                this.articles = response.articles || [];
                this.totalPages = response.totalPages || 1;
                this.totalArticles = response.total || 0;
                return response;
            }
        } catch (error) {
            console.error('Load articles error:', error);
            throw error;
        }
    }
    
    async loadCategories() {
        try {
            const response = await API.getCategories();
            if (response.success) {
                this.categories = response.categories || [];
                return response;
            }
        } catch (error) {
            console.error('Load categories error:', error);
            throw error;
        }
    }
    
    async loadStats() {
        try {
            const response = await API.getStats();
            if (response.success) {
                this.stats = response.stats || {};
                return response;
            }
        } catch (error) {
            console.error('Load stats error:', error);
            // 统计数据加载失败不影响主要功能
            this.stats = {
                totalArticles: 0,
                totalViews: 0,
                totalComments: 0
            };
        }
    }
    
    // ========== 渲染方法 ==========
    renderFeaturedArticles() {
        const container = document.getElementById('featuredArticles');
        if (!container) return;
        
        // 获取前6篇文章作为特色文章
        const featuredArticles = this.articles.slice(0, CONFIG.DEFAULTS.FEATURED_ARTICLES_COUNT);
        
        if (featuredArticles.length === 0) {
            container.innerHTML = '<div class="no-articles">暂无文章</div>';
            return;
        }
        
        const articlesHTML = featuredArticles.map(article => `
            <div class="featured-article" onclick="App.openArticle('${article._id}')">
                <div class="featured-article-bg"></div>
                <div class="featured-article-overlay"></div>
                <div class="featured-article-content">
                    <h3 class="featured-article-title">${article.title}</h3>
                    <div class="featured-article-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(article.createdAt, 'SHORT')}</span>
                        <span><i class="fas fa-eye"></i> ${Utils.formatNumber(article.views || 0)}</span>
                        <span><i class="fas fa-comments"></i> ${Utils.formatNumber(article.comments?.length || 0)}</span>
                    </div>
                    <p class="featured-article-excerpt">${Utils.truncateText(article.content, 120)}</p>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = articlesHTML;
    }
    
    renderArticles() {
        const container = document.getElementById('articlesGrid');
        if (!container) return;
        
        if (this.articles.length === 0) {
            container.innerHTML = '<div class="no-articles">暂无文章</div>';
            return;
        }
        
        const articlesHTML = this.articles.map(article => `
            <div class="article-card" onclick="App.openArticle('${article._id}')">
                <div class="article-card-header">
                    <div class="article-card-category">${article.category || '未分类'}</div>
                </div>
                <div class="article-card-content">
                    <h3 class="article-card-title">${article.title}</h3>
                    <p class="article-card-excerpt">${Utils.truncateText(article.content)}</p>
                    <div class="article-card-meta">
                        <div class="article-card-author">
                            <i class="fas fa-user"></i>
                            <span>${article.author?.username || '匿名'}</span>
                        </div>
                        <div class="article-card-stats">
                            <div class="article-card-stat">
                                <i class="fas fa-eye"></i>
                                <span>${Utils.formatNumber(article.views || 0)}</span>
                            </div>
                            <div class="article-card-stat">
                                <i class="fas fa-comments"></i>
                                <span>${Utils.formatNumber(article.comments?.length || 0)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = articlesHTML;
        
        // 渲染分页
        this.renderPagination();
    }
    
    renderCategories() {
        const container = document.getElementById('categoriesGrid');
        const filterSelect = document.getElementById('categoryFilter');
        
        if (!container || !filterSelect) return;
        
        // 渲染分类网格
        if (this.categories.length === 0) {
            container.innerHTML = '<div class="no-categories">暂无分类</div>';
        } else {
            const categoriesHTML = this.categories.map(category => `
                <div class="category-card" onclick="App.filterByCategory('${category.name}')">
                    <div class="category-card-icon">
                        <i class="${Utils.getCategoryIcon(category.name)}"></i>
                    </div>
                    <h3 class="category-card-title">${category.name}</h3>
                    <div class="category-card-count">${category.articleCount || 0} 篇文章</div>
                    <p class="category-card-description">${category.description || '暂无描述'}</p>
                </div>
            `).join('');
            
            container.innerHTML = categoriesHTML;
        }
        
        // 渲染分类筛选器
        const filterOptions = this.categories.map(category => 
            `<option value="${category.name}">${category.name}</option>`
        ).join('');
        
        filterSelect.innerHTML = `
            <option value="">所有分类</option>
            ${filterOptions}
        `;
    }
    
    renderStats() {
        const totalArticlesEl = document.getElementById('totalArticles');
        const totalViewsEl = document.getElementById('totalViews');
        const totalCommentsEl = document.getElementById('totalComments');
        
        if (totalArticlesEl) {
            this.animateNumber(totalArticlesEl, this.stats.totalArticles || 0);
        }
        if (totalViewsEl) {
            this.animateNumber(totalViewsEl, this.stats.totalViews || 0);
        }
        if (totalCommentsEl) {
            this.animateNumber(totalCommentsEl, this.stats.totalComments || 0);
        }
    }
    
    renderPagination() {
        const container = document.getElementById('pagination');
        if (!container || this.totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一页按钮
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="App.changePage(${this.currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="App.changePage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="App.changePage(${i})">${i}</button>
            `;
        }
        
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
            paginationHTML += `<button class="pagination-btn" onclick="App.changePage(${this.totalPages})">${this.totalPages}</button>`;
        }
        
        // 下一页按钮
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === this.totalPages ? 'disabled' : ''} 
                    onclick="App.changePage(${this.currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        container.innerHTML = paginationHTML;
    }

    renderSidebar() {
        this.renderPopularArticles();
        this.renderTagCloud();
        this.renderRecentComments();
        this.renderArchive();
    }

    renderPopularArticles() {
        const container = document.getElementById('popularArticles');
        if (!container) return;

        // 按浏览量排序获取热门文章
        const popularArticles = [...this.articles]
            .sort((a, b) => (b.views || 0) - (a.views || 0))
            .slice(0, 5);

        if (popularArticles.length === 0) {
            container.innerHTML = '<div class="no-content">暂无热门文章</div>';
            return;
        }

        const articlesHTML = popularArticles.map((article, index) => `
            <div class="popular-article" onclick="App.openArticle('${article._id}')">
                <div class="popular-article-index">${index + 1}</div>
                <div class="popular-article-content">
                    <div class="popular-article-title">${article.title}</div>
                    <div class="popular-article-meta">
                        <span><i class="fas fa-eye"></i> ${Utils.formatNumber(article.views || 0)}</span>
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(article.createdAt, 'SHORT')}</span>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = articlesHTML;
    }

    renderTagCloud() {
        const container = document.getElementById('tagCloud');
        if (!container) return;

        // 收集所有标签并统计频率
        const tagCounts = {};
        this.articles.forEach(article => {
            if (article.tags) {
                article.tags.forEach(tag => {
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
            }
        });

        const tags = Object.entries(tagCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 20);

        if (tags.length === 0) {
            container.innerHTML = '<div class="no-content">暂无标签</div>';
            return;
        }

        const maxCount = Math.max(...tags.map(([,count]) => count));

        const tagsHTML = tags.map(([tag, count]) => {
            let sizeClass = '';
            if (count === maxCount) sizeClass = 'large';
            else if (count >= maxCount * 0.6) sizeClass = 'medium';

            return `<span class="tag-cloud-item ${sizeClass}" onclick="App.filterByTag('${tag}')">${tag}</span>`;
        }).join('');

        container.innerHTML = tagsHTML;
    }

    renderRecentComments() {
        const container = document.getElementById('recentComments');
        if (!container) return;

        // 模拟最新评论数据
        const recentComments = [
            {
                author: '技术爱好者',
                content: '这篇关于Node.js的文章写得很详细，对我帮助很大！',
                articleTitle: 'Node.js 后端开发最佳实践',
                createdAt: new Date('2024-01-16')
            },
            {
                author: '前端小白',
                content: '前端工具链的介绍很全面，正好在学习这方面的内容。',
                articleTitle: '现代前端开发工具链',
                createdAt: new Date('2024-01-15')
            },
            {
                author: '数据库新手',
                content: 'MongoDB的聚合管道确实很强大，感谢分享！',
                articleTitle: 'MongoDB 数据库设计指南',
                createdAt: new Date('2024-01-14')
            }
        ];

        const commentsHTML = recentComments.map(comment => `
            <div class="recent-comment">
                <div class="recent-comment-author">${comment.author}</div>
                <div class="recent-comment-content">${comment.content}</div>
                <div class="recent-comment-meta">
                    ${Utils.formatDate(comment.createdAt, 'SHORT')} · ${comment.articleTitle}
                </div>
            </div>
        `).join('');

        container.innerHTML = commentsHTML;
    }

    renderArchive() {
        const container = document.getElementById('archiveList');
        if (!container) return;

        // 按月份归档文章
        const archives = {};
        this.articles.forEach(article => {
            const date = new Date(article.createdAt);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthName = `${date.getFullYear()}年${date.getMonth() + 1}月`;

            if (!archives[monthKey]) {
                archives[monthKey] = {
                    name: monthName,
                    count: 0
                };
            }
            archives[monthKey].count++;
        });

        const archiveList = Object.entries(archives)
            .sort(([a], [b]) => b.localeCompare(a))
            .slice(0, 12);

        if (archiveList.length === 0) {
            container.innerHTML = '<div class="no-content">暂无归档</div>';
            return;
        }

        const archiveHTML = archiveList.map(([key, archive]) => `
            <div class="archive-item" onclick="App.filterByMonth('${key}')">
                <span class="archive-month">${archive.name}</span>
                <span class="archive-count">${archive.count}</span>
            </div>
        `).join('');

        container.innerHTML = archiveHTML;
    }
    
    // ========== 事件绑定 ==========
    bindEvents() {
        // 分类筛选
        const categoryFilter = document.getElementById('categoryFilter');
        categoryFilter?.addEventListener('change', (e) => {
            this.filterByCategory(e.target.value);
        });
        
        // 排序筛选
        const sortFilter = document.getElementById('sortFilter');
        sortFilter?.addEventListener('change', (e) => {
            this.sortArticles(e.target.value);
        });
        
        // 监听认证状态变化
        EventBus.on('auth:status-changed', () => {
            // 重新加载数据以获取用户相关内容
            this.loadArticles();
        });

        // 侧边栏搜索
        const sidebarSearch = document.getElementById('sidebarSearch');
        if (sidebarSearch) {
            sidebarSearch.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.handleSidebarSearch();
                }
            });
        }
    }

    // 处理侧边栏搜索
    handleSidebarSearch() {
        const searchInput = document.getElementById('sidebarSearch');
        if (searchInput) {
            const query = searchInput.value.trim();
            if (query) {
                UI.showSearchResults(query);
                searchInput.value = '';
            }
        }
    }

    // 按标签筛选
    async filterByTag(tag) {
        try {
            UI.showLoading();

            // 筛选包含该标签的文章
            const filteredArticles = this.articles.filter(article =>
                article.tags && article.tags.includes(tag)
            );

            // 临时保存原始数据
            const originalArticles = this.articles;
            this.articles = filteredArticles;
            this.currentPage = 1;
            this.totalPages = 1;
            this.totalArticles = filteredArticles.length;

            this.renderArticles();
            UI.hideLoading();

            // 滚动到文章区域
            UI.scrollToSection('articles');

            // 显示筛选提示
            UI.showToast(`正在显示标签"${tag}"的文章 (${filteredArticles.length}篇)`, 'info');

            // 恢复原始数据（可选，或者添加重置按钮）
            setTimeout(() => {
                this.articles = originalArticles;
            }, 100);

        } catch (error) {
            UI.hideLoading();
            UI.showToast('筛选失败', 'error');
        }
    }

    // 按月份筛选
    async filterByMonth(monthKey) {
        try {
            UI.showLoading();

            const [year, month] = monthKey.split('-');
            const filteredArticles = this.articles.filter(article => {
                const date = new Date(article.createdAt);
                return date.getFullYear() === parseInt(year) &&
                       (date.getMonth() + 1) === parseInt(month);
            });

            // 临时保存原始数据
            const originalArticles = this.articles;
            this.articles = filteredArticles;
            this.currentPage = 1;
            this.totalPages = 1;
            this.totalArticles = filteredArticles.length;

            this.renderArticles();
            UI.hideLoading();

            // 滚动到文章区域
            UI.scrollToSection('articles');

            // 显示筛选提示
            const monthName = `${year}年${month}月`;
            UI.showToast(`正在显示${monthName}的文章 (${filteredArticles.length}篇)`, 'info');

            // 恢复原始数据
            setTimeout(() => {
                this.articles = originalArticles;
            }, 100);

        } catch (error) {
            UI.hideLoading();
            UI.showToast('筛选失败', 'error');
        }
    }
    
    // ========== 交互方法 ==========
    async changePage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        
        try {
            UI.showLoading();
            this.currentPage = page;
            await this.loadArticles();
            this.renderArticles();
            UI.hideLoading();
            
            // 滚动到文章区域
            UI.scrollToSection('articles');
            
            // 更新URL
            Utils.setUrlParams({ page: page > 1 ? page : null });
            
        } catch (error) {
            UI.hideLoading();
            UI.showToast(handleApiError(error), 'error');
        }
    }
    
    async filterByCategory(category) {
        try {
            UI.showLoading();
            this.currentCategory = category;
            this.currentPage = 1;
            
            await this.loadArticles();
            this.renderArticles();
            UI.hideLoading();
            
            // 更新筛选器状态
            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                categoryFilter.value = category;
            }
            
            // 滚动到文章区域
            UI.scrollToSection('articles');
            
            // 更新URL
            Utils.setUrlParams({ 
                category: category || null, 
                page: null 
            });
            
        } catch (error) {
            UI.hideLoading();
            UI.showToast(handleApiError(error), 'error');
        }
    }
    
    async sortArticles(sort) {
        try {
            UI.showLoading();
            this.currentSort = sort;
            this.currentPage = 1;
            
            await this.loadArticles();
            this.renderArticles();
            UI.hideLoading();
            
            // 更新排序器状态
            const sortFilter = document.getElementById('sortFilter');
            if (sortFilter) {
                sortFilter.value = sort;
            }
            
            // 更新URL
            Utils.setUrlParams({ 
                sort: sort !== 'date' ? sort : null, 
                page: null 
            });
            
        } catch (error) {
            UI.hideLoading();
            UI.showToast(handleApiError(error), 'error');
        }
    }
    
    async openArticle(articleId) {
        try {
            UI.showLoading();
            const response = await API.getArticle(articleId);
            UI.hideLoading();
            
            if (response.success && response.article) {
                this.displayArticleModal(response.article);
            }
        } catch (error) {
            UI.hideLoading();
            UI.showToast(handleApiError(error), 'error');
        }
    }
    
    displayArticleModal(article) {
        const modalBody = document.getElementById('articleModalBody');
        if (!modalBody) return;
        
        modalBody.innerHTML = `
            <article class="article-detail">
                <header class="article-header">
                    <div class="article-category">${article.category || '未分类'}</div>
                    <h1 class="article-title">${article.title}</h1>
                    <div class="article-meta">
                        <div class="article-author">
                            <i class="fas fa-user"></i>
                            <span>${article.author?.username || '匿名'}</span>
                        </div>
                        <div class="article-date">
                            <i class="fas fa-calendar"></i>
                            <span>${Utils.formatDate(article.createdAt)}</span>
                        </div>
                        <div class="article-stats">
                            <span><i class="fas fa-eye"></i> ${Utils.formatNumber(article.views || 0)}</span>
                            <span><i class="fas fa-comments"></i> ${Utils.formatNumber(article.comments?.length || 0)}</span>
                        </div>
                    </div>
                </header>
                <div class="article-content">
                    ${this.formatArticleContent(article.content)}
                </div>
                <footer class="article-footer">
                    <div class="article-tags">
                        ${(article.tags || []).map(tag => `<span class="tag">#${tag}</span>`).join('')}
                    </div>
                </footer>
            </article>
        `;
        
        UI.openModal('articleModal');
    }
    
    formatArticleContent(content) {
        // 简单的内容格式化，将换行转换为段落
        return content
            .split('\n\n')
            .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
            .join('');
    }
    
    // ========== 工具方法 ==========
    animateNumber(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Utils.formatNumber(Math.floor(current));
        }, 16);
    }
    
    handleUrlParams() {
        const params = Utils.getUrlParams();

        if (params.category) {
            this.filterByCategory(params.category);
        }

        if (params.sort) {
            this.sortArticles(params.sort);
        }

        if (params.page) {
            this.changePage(parseInt(params.page));
        }
    }

    // 显示离线模式
    showOfflineMode() {
        console.log('启用离线模式，使用示例数据');

        // 使用示例数据
        this.articles = this.getSampleArticles();
        this.categories = this.getSampleCategories();
        this.stats = this.getSampleStats();
        this.totalPages = 1;
        this.totalArticles = this.articles.length;

        // 渲染页面
        this.renderFeaturedArticles();
        this.renderArticles();
        this.renderCategories();
        this.renderStats();

        // 显示离线提示
        UI.showToast('当前为离线模式，显示示例数据', 'warning', 5000);
    }

    // 获取示例文章数据
    getSampleArticles() {
        return [
            {
                _id: '1',
                title: 'Node.js 后端开发最佳实践',
                content: 'Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行时环境，它让 JavaScript 可以在服务器端运行。本文将介绍 Node.js 后端开发的最佳实践，包括项目结构、错误处理、安全性等方面的内容。\n\n在现代 Web 开发中，Node.js 已经成为了后端开发的重要选择。它不仅性能优异，而且拥有丰富的生态系统。通过合理的架构设计和最佳实践，我们可以构建出高性能、可维护的后端应用。',
                category: '技术',
                author: { username: '技术博主' },
                createdAt: new Date('2024-01-15'),
                views: 1250,
                comments: [{ id: 1 }, { id: 2 }, { id: 3 }],
                tags: ['Node.js', 'JavaScript', '后端开发']
            },
            {
                _id: '2',
                title: 'MongoDB 数据库设计指南',
                content: 'MongoDB 是一个基于文档的 NoSQL 数据库，它提供了灵活的数据模型和强大的查询功能。本文将详细介绍 MongoDB 的数据库设计原则、索引优化、聚合管道等核心概念。\n\n与传统的关系型数据库不同，MongoDB 采用文档存储模式，这使得它在处理复杂数据结构时更加灵活。通过合理的数据建模和索引策略，我们可以充分发挥 MongoDB 的性能优势。',
                category: '技术',
                author: { username: '数据库专家' },
                createdAt: new Date('2024-01-10'),
                views: 980,
                comments: [{ id: 1 }, { id: 2 }],
                tags: ['MongoDB', '数据库', 'NoSQL']
            },
            {
                _id: '3',
                title: '现代前端开发工具链',
                content: '现代前端开发涉及众多工具和技术，从构建工具到测试框架，从代码规范到部署流程。本文将介绍一套完整的前端开发工具链，帮助开发者提高开发效率。\n\n随着前端技术的快速发展，开发工具链也变得越来越复杂。选择合适的工具组合，建立高效的开发流程，对于项目的成功至关重要。',
                category: '技术',
                author: { username: '前端工程师' },
                createdAt: new Date('2024-01-08'),
                views: 1580,
                comments: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }],
                tags: ['前端', 'JavaScript', '工具链']
            },
            {
                _id: '4',
                title: 'RESTful API 设计规范',
                content: 'RESTful API 是现代 Web 应用的重要组成部分。本文将介绍 RESTful API 的设计原则、HTTP 状态码的使用、错误处理机制等内容，帮助开发者设计出优雅的 API。\n\n良好的 API 设计不仅能提高开发效率，还能改善用户体验。遵循 RESTful 设计原则，我们可以创建出直观、易用的 API 接口。',
                category: '技术',
                author: { username: 'API 设计师' },
                createdAt: new Date('2024-01-05'),
                views: 750,
                comments: [{ id: 1 }],
                tags: ['API', 'REST', 'Web开发']
            },
            {
                _id: '5',
                title: '我的编程学习之路',
                content: '回顾我从编程小白到全栈开发者的成长历程，分享学习过程中的心得体会、遇到的困难以及解决方法。希望能给正在学习编程的朋友们一些启发和帮助。\n\n编程学习是一个持续的过程，需要不断地实践和思考。在这个过程中，我遇到了很多挑战，也收获了很多成长。希望我的经验能够帮助到更多的学习者。',
                category: '生活',
                author: { username: '全栈开发者' },
                createdAt: new Date('2024-01-03'),
                views: 2100,
                comments: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }],
                tags: ['编程', '学习', '成长']
            },
            {
                _id: '6',
                title: '开源项目贡献指南',
                content: '参与开源项目是提升编程技能的好方法。本文将介绍如何选择合适的开源项目、如何提交 Pull Request、如何与社区互动等内容。\n\n开源社区是一个充满活力的地方，在这里你可以学习到最新的技术，结识志同道合的朋友，为开源事业贡献自己的力量。',
                category: '技术',
                author: { username: '开源贡献者' },
                createdAt: new Date('2024-01-01'),
                views: 890,
                comments: [{ id: 1 }, { id: 2 }],
                tags: ['开源', 'GitHub', '社区']
            }
        ];
    }

    // 获取示例分类数据
    getSampleCategories() {
        return [
            {
                name: '技术',
                description: '编程技术、开发工具、最佳实践',
                articleCount: 5
            },
            {
                name: '生活',
                description: '个人感悟、生活分享、成长经历',
                articleCount: 1
            },
            {
                name: '读书',
                description: '读书笔记、书籍推荐、知识总结',
                articleCount: 0
            },
            {
                name: '旅行',
                description: '旅行游记、风景分享、文化体验',
                articleCount: 0
            }
        ];
    }

    // 获取示例统计数据
    getSampleStats() {
        return {
            totalArticles: 6,
            totalViews: 8550,
            totalComments: 20
        };
    }
}

// ========== 初始化应用 ==========
window.App = new BlogApp();
