// ========== 认证管理器 ==========
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.init();
    }
    
    // 初始化认证状态
    init() {
        const token = localStorage.getItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        const userInfo = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_INFO);
        
        if (token && userInfo) {
            try {
                this.currentUser = JSON.parse(userInfo);
                this.isAuthenticated = true;
                this.updateUI();
            } catch (error) {
                console.error('Failed to parse user info:', error);
                this.logout();
            }
        }
        
        // 监听认证事件
        EventBus.on('auth:login', (data) => this.handleLogin(data));
        EventBus.on('auth:logout', () => this.handleLogout());
        EventBus.on('auth:register', (data) => this.handleRegister(data));
    }
    
    // 处理登录
    async handleLogin(data) {
        try {
            const response = await API.login(data);
            
            if (response.success && response.token) {
                localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, response.token);
                localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(response.user));
                
                this.currentUser = response.user;
                this.isAuthenticated = true;
                this.updateUI();
                
                UI.showToast(CONFIG.SUCCESS_MESSAGES.LOGIN_SUCCESS, 'success');
                UI.closeModal('authModal');
                
                return { success: true };
            }
        } catch (error) {
            const message = handleApiError(error);
            UI.showToast(message, 'error');
            return { success: false, message };
        }
    }
    
    // 处理注册
    async handleRegister(data) {
        try {
            const response = await API.register(data);
            
            if (response.success) {
                UI.showToast(CONFIG.SUCCESS_MESSAGES.REGISTER_SUCCESS, 'success');
                
                // 自动登录
                return this.handleLogin({
                    email: data.email,
                    password: data.password
                });
            }
        } catch (error) {
            const message = handleApiError(error);
            UI.showToast(message, 'error');
            return { success: false, message };
        }
    }
    
    // 处理登出
    handleLogout() {
        localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_INFO);
        
        this.currentUser = null;
        this.isAuthenticated = false;
        this.updateUI();
        
        UI.showToast(CONFIG.SUCCESS_MESSAGES.LOGOUT_SUCCESS, 'success');
    }
    
    // 登出
    async logout() {
        try {
            await API.logout();
            this.handleLogout();
        } catch (error) {
            console.error('Logout error:', error);
            this.handleLogout(); // 强制登出
        }
    }
    
    // 检查用户权限
    hasPermission(permission) {
        if (!this.isAuthenticated || !this.currentUser) {
            return false;
        }
        
        // 管理员拥有所有权限
        if (this.currentUser.role === 'admin') {
            return true;
        }
        
        // 根据权限类型检查
        switch (permission) {
            case 'create_article':
            case 'create_comment':
                return true; // 所有登录用户都可以创建文章和评论
            case 'edit_article':
            case 'delete_article':
                return this.currentUser.role === 'admin'; // 只有管理员可以编辑/删除文章
            case 'delete_comment':
                return this.currentUser.role === 'admin'; // 只有管理员可以删除评论
            case 'manage_categories':
                return this.currentUser.role === 'admin'; // 只有管理员可以管理分类
            default:
                return false;
        }
    }
    
    // 更新UI状态
    updateUI() {
        const authButton = document.getElementById('authToggle');
        const authIcon = authButton?.querySelector('i');
        
        if (this.isAuthenticated && this.currentUser) {
            // 更新认证按钮
            if (authIcon) {
                authIcon.className = 'fas fa-user-circle';
            }
            authButton?.setAttribute('title', `${this.currentUser.username} (${this.currentUser.role})`);
            
            // 显示用户相关的UI元素
            document.querySelectorAll('[data-auth="required"]').forEach(el => {
                el.style.display = '';
            });
            
            // 隐藏未认证用户的UI元素
            document.querySelectorAll('[data-auth="guest"]').forEach(el => {
                el.style.display = 'none';
            });
            
            // 根据权限显示/隐藏管理员功能
            const adminElements = document.querySelectorAll('[data-role="admin"]');
            adminElements.forEach(el => {
                el.style.display = this.currentUser.role === 'admin' ? '' : 'none';
            });
            
        } else {
            // 更新认证按钮
            if (authIcon) {
                authIcon.className = 'fas fa-user';
            }
            authButton?.setAttribute('title', '登录/注册');
            
            // 隐藏需要认证的UI元素
            document.querySelectorAll('[data-auth="required"]').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示未认证用户的UI元素
            document.querySelectorAll('[data-auth="guest"]').forEach(el => {
                el.style.display = '';
            });
            
            // 隐藏管理员功能
            document.querySelectorAll('[data-role="admin"]').forEach(el => {
                el.style.display = 'none';
            });
        }
        
        // 发送认证状态更新事件
        EventBus.emit('auth:status-changed', {
            isAuthenticated: this.isAuthenticated,
            user: this.currentUser
        });
    }
    
    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }
    
    // 检查是否已认证
    isLoggedIn() {
        return this.isAuthenticated;
    }
    
    // 检查是否为管理员
    isAdmin() {
        return this.isAuthenticated && this.currentUser?.role === 'admin';
    }
    
    // 刷新用户信息
    async refreshUserInfo() {
        if (!this.isAuthenticated) return;
        
        try {
            const response = await API.getCurrentUser();
            if (response.success && response.user) {
                this.currentUser = response.user;
                localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(response.user));
                this.updateUI();
            }
        } catch (error) {
            console.error('Failed to refresh user info:', error);
            // 如果获取用户信息失败，可能token已过期
            if (error.status === 401) {
                this.handleLogout();
            }
        }
    }
}

// ========== 认证表单管理 ==========
class AuthFormManager {
    constructor() {
        this.currentMode = 'login'; // login 或 register
        this.init();
    }
    
    init() {
        // 监听认证按钮点击
        document.getElementById('authToggle')?.addEventListener('click', () => {
            if (Auth.isLoggedIn()) {
                this.showUserMenu();
            } else {
                this.showAuthModal();
            }
        });
        
        // 监听模态框关闭
        document.getElementById('authModalClose')?.addEventListener('click', () => {
            UI.closeModal('authModal');
        });
    }
    
    // 显示用户菜单
    showUserMenu() {
        const menu = document.createElement('div');
        menu.className = 'user-menu';
        menu.innerHTML = `
            <div class="user-menu-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">${Auth.getCurrentUser()?.username}</div>
                        <div class="user-role">${Auth.getCurrentUser()?.role}</div>
                    </div>
                </div>
                <div class="user-menu-actions">
                    <button class="menu-item" onclick="Auth.logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        退出登录
                    </button>
                </div>
            </div>
        `;
        
        // 添加样式和定位逻辑
        document.body.appendChild(menu);
        
        // 点击外部关闭菜单
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);
    }
    
    // 显示认证模态框
    showAuthModal(mode = 'login') {
        this.currentMode = mode;
        this.renderAuthForm();
        UI.openModal('authModal');
    }
    
    // 渲染认证表单
    renderAuthForm() {
        const modalTitle = document.getElementById('authModalTitle');
        const modalBody = document.getElementById('authModalBody');
        
        if (this.currentMode === 'login') {
            modalTitle.textContent = '登录';
            modalBody.innerHTML = this.getLoginFormHTML();
        } else {
            modalTitle.textContent = '注册';
            modalBody.innerHTML = this.getRegisterFormHTML();
        }
        
        this.bindFormEvents();
    }
    
    // 获取登录表单HTML
    getLoginFormHTML() {
        return `
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-input" name="email" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" name="password" required>
                </div>
                <button type="submit" class="btn-primary" style="width: 100%;">登录</button>
                <div class="auth-switch">
                    还没有账号？<a href="#" onclick="AuthForm.showAuthModal('register')">立即注册</a>
                </div>
            </form>
        `;
    }
    
    // 获取注册表单HTML
    getRegisterFormHTML() {
        return `
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" name="username" required>
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-input" name="email" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" name="password" required>
                </div>
                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <input type="password" class="form-input" name="confirmPassword" required>
                </div>
                <button type="submit" class="btn-primary" style="width: 100%;">注册</button>
                <div class="auth-switch">
                    已有账号？<a href="#" onclick="AuthForm.showAuthModal('login')">立即登录</a>
                </div>
            </form>
        `;
    }
    
    // 绑定表单事件
    bindFormEvents() {
        const form = document.querySelector('.auth-form');
        if (!form) return;
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            if (this.currentMode === 'register') {
                // 验证密码确认
                if (data.password !== data.confirmPassword) {
                    UI.showToast('两次输入的密码不一致', 'error');
                    return;
                }
                
                // 验证密码强度
                const passwordValidation = Utils.validatePassword(data.password);
                if (!passwordValidation.isValid) {
                    UI.showToast('密码长度至少6位', 'error');
                    return;
                }
                
                delete data.confirmPassword;
                EventBus.emit('auth:register', data);
            } else {
                EventBus.emit('auth:login', data);
            }
        });
    }
}

// ========== 初始化认证系统 ==========
window.Auth = new AuthManager();
window.AuthForm = new AuthFormManager();
